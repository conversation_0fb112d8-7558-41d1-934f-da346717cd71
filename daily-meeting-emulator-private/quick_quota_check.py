#!/usr/bin/env python3
"""
Быстрая проверка квот Gemini моделей
"""

import google.generativeai as genai
import time
import os

def test_models():
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY не установлен")
        return
    
    genai.configure(api_key=api_key)
    
    # Приоритетные модели для тестирования (по ожидаемой производительности)
    priority_models = [
        # Топ модели с высокими квотами
        'gemini-1.5-flash-8b-latest',    # 2000 req/min - ЛУЧШИЙ
        'gemini-1.5-flash-8b',           # 2000 req/min - ЛУЧШИЙ  
        'gemini-1.5-flash-8b-001',       # 2000 req/min - ЛУЧШИЙ
        
        # Gemini 2.0 модели
        'gemini-2.0-flash-exp',          # 1000 req/min - ОТЛИЧНЫЙ
        'gemini-2.0-flash',              # 1000 req/min - ОТЛИЧНЫЙ
        'gemini-2.0-flash-001',          # 1000 req/min - ОТЛИЧНЫЙ
        'gemini-exp-1206',               # 1000 req/min - ОТЛИЧНЫЙ
        
        # Gemini 1.5 Flash модели
        'gemini-1.5-flash-latest',       # 1000 req/min - ХОРОШИЙ
        'gemini-1.5-flash',              # 1000 req/min - ХОРОШИЙ
        'gemini-1.5-flash-002',          # 1000 req/min - ХОРОШИЙ
        
        # Gemini 2.5 экспериментальные
        'gemini-2.5-flash-preview-05-20', # 1000 req/min - НОВЫЙ
        'gemini-2.5-flash-preview-04-17', # 1000 req/min - НОВЫЙ
        
        # Lite модели (быстрые)
        'gemini-2.0-flash-lite',         # Быстрые, но меньше квота
        'gemini-2.0-flash-lite-001',
        
        # Pro модели (меньше квота, но лучше качество)
        'gemini-1.5-pro-latest',         # 360 req/min
        'gemini-1.5-pro',                # 360 req/min
        'gemini-2.0-pro-exp',            # 360 req/min
    ]
    
    # Известная информация о квотах
    quota_info = {
        'gemini-1.5-flash-8b-latest': {'rpm': 2000, 'rpd': 50000, 'tier': '🔥 УЛЬТРА', 'realtime': 'ИДЕАЛЬНО'},
        'gemini-1.5-flash-8b': {'rpm': 2000, 'rpd': 50000, 'tier': '🔥 УЛЬТРА', 'realtime': 'ИДЕАЛЬНО'},
        'gemini-1.5-flash-8b-001': {'rpm': 2000, 'rpd': 50000, 'tier': '🔥 УЛЬТРА', 'realtime': 'ИДЕАЛЬНО'},
        
        'gemini-2.0-flash-exp': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'ОТЛИЧНО'},
        'gemini-2.0-flash': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'ОТЛИЧНО'},
        'gemini-2.0-flash-001': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'ОТЛИЧНО'},
        'gemini-exp-1206': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'ОТЛИЧНО'},
        
        'gemini-1.5-flash-latest': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'ХОРОШО'},
        'gemini-1.5-flash': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'ХОРОШО'},
        'gemini-1.5-flash-002': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'ХОРОШО'},
        
        'gemini-2.5-flash-preview-05-20': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'НОВЫЙ'},
        'gemini-2.5-flash-preview-04-17': {'rpm': 1000, 'rpd': 50000, 'tier': '🟢 ВЫСОКИЙ', 'realtime': 'НОВЫЙ'},
        
        'gemini-2.0-flash-lite': {'rpm': 1000, 'rpd': 50000, 'tier': '🟡 СРЕДНИЙ', 'realtime': 'БЫСТРО'},
        'gemini-2.0-flash-lite-001': {'rpm': 1000, 'rpd': 50000, 'tier': '🟡 СРЕДНИЙ', 'realtime': 'БЫСТРО'},
        
        'gemini-1.5-pro-latest': {'rpm': 360, 'rpd': 50000, 'tier': '🟡 СРЕДНИЙ', 'realtime': 'МЕДЛЕННО'},
        'gemini-1.5-pro': {'rpm': 360, 'rpd': 50000, 'tier': '🟡 СРЕДНИЙ', 'realtime': 'МЕДЛЕННО'},
        'gemini-2.0-pro-exp': {'rpm': 360, 'rpd': 50000, 'tier': '🟡 СРЕДНИЙ', 'realtime': 'МЕДЛЕННО'},
    }
    
    print('🔍 ПРОВЕРКА ТОПОВЫХ GEMINI МОДЕЛЕЙ ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ')
    print('=' * 80)
    
    working_models = []
    failed_models = []
    
    for i, model_name in enumerate(priority_models, 1):
        print(f'[{i}/{len(priority_models)}] Тестируем {model_name}...')
        
        try:
            start_time = time.time()
            model = genai.GenerativeModel(model_name)
            
            # Тест с реалистичным промптом для диалога
            response = model.generate_content(
                'You are Sarah, a senior developer. Give a brief standup update about fixing a bug.',
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=80,
                    temperature=0.8
                )
            )
            
            response_time = time.time() - start_time
            
            if response.text and len(response.text.strip()) > 10:
                working_models.append((model_name, response_time, len(response.text)))
                print(f'   ✅ РАБОТАЕТ: {response_time:.2f}s, {len(response.text)} символов')
                print(f'   💬 "{response.text[:50]}..."')
            else:
                failed_models.append((model_name, 'Пустой ответ'))
                print(f'   ❌ Пустой ответ')
                
        except Exception as e:
            error_msg = str(e)
            failed_models.append((model_name, error_msg))
            
            if '429' in error_msg:
                print(f'   ⚠️  КВОТА ПРЕВЫШЕНА: {error_msg[:60]}...')
            elif '404' in error_msg:
                print(f'   🗑️  УСТАРЕЛА: {error_msg[:60]}...')
            else:
                print(f'   ❌ ОШИБКА: {error_msg[:60]}...')
        
        print()
        time.sleep(0.3)  # Небольшая задержка
    
    # Генерируем саммари
    print('\n' + '='*80)
    print('📊 САММАРИ ПО КВОТАМ И ДОСТУПНОСТИ')
    print('='*80)
    
    print(f'✅ Работающих моделей: {len(working_models)}')
    print(f'❌ Недоступных моделей: {len(failed_models)}')
    print()
    
    if working_models:
        print('🏆 ТОП РЕКОМЕНДАЦИИ ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ:')
        print('-' * 60)
        
        # Сортируем по скорости ответа
        working_models.sort(key=lambda x: x[1])
        
        for i, (model_name, response_time, length) in enumerate(working_models[:5], 1):
            info = quota_info.get(model_name, {'rpm': 'неизвестно', 'tier': '❓', 'realtime': 'неизвестно'})
            
            print(f'{i}. {model_name}')
            print(f'   {info["tier"]} | ⚡ {response_time:.2f}s | 📊 {info["rpm"]} req/min')
            print(f'   🎯 Для реального времени: {info["realtime"]}')
            if isinstance(info["rpm"], int):
                print(f'   💡 Макс скорость: {info["rpm"]/60:.1f} запросов/секунду')
                print(f'   🛡️  Безопасный лимит: {info["rpm"]*0.8:.0f} запросов/минуту')
            print()
    
    # Анализ квот
    print('💰 АНАЛИЗ КВОТ:')
    print('-' * 40)
    
    ultra_high = [m for m in working_models if quota_info.get(m[0], {}).get('rpm', 0) >= 2000]
    high = [m for m in working_models if 1000 <= quota_info.get(m[0], {}).get('rpm', 0) < 2000]
    medium = [m for m in working_models if 100 <= quota_info.get(m[0], {}).get('rpm', 0) < 1000]
    
    print(f'🔥 Ультра-высокие (≥2000 req/min): {len(ultra_high)} - ЛУЧШИЕ ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ')
    print(f'🟢 Высокие (1000-1999 req/min): {len(high)} - ОТЛИЧНЫЕ ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ')
    print(f'🟡 Средние (100-999 req/min): {len(medium)} - ПОДХОДЯТ С ОГРАНИЧЕНИЯМИ')
    
    print('\n🔄 ИНФОРМАЦИЯ О ВОССТАНОВЛЕНИИ КВОТ:')
    print('-' * 50)
    print('• Минутные лимиты: Скользящее окно 60 секунд')
    print('• Дневные лимиты: Сброс в полночь UTC')
    print('• Burst запросы: Кратковременные всплески разрешены')
    print('• Восстановление: Постепенное в течение минуты')
    
    # Финальная рекомендация
    if working_models:
        best_model = working_models[0]
        best_info = quota_info.get(best_model[0], {})
        
        print(f'\n🎯 ИТОГОВАЯ РЕКОМЕНДАЦИЯ:')
        print('='*50)
        print(f'🏆 ИСПОЛЬЗУЙТЕ: {best_model[0]}')
        print(f'⚡ Скорость: {best_model[1]:.2f} секунды')
        print(f'📊 Квота: {best_info.get("rpm", "неизвестно")} запросов/минуту')
        print(f'🎯 Оценка: {best_info.get("realtime", "неизвестно")} для реального времени')
        
        if isinstance(best_info.get("rpm"), int):
            rpm = best_info["rpm"]
            print(f'\n📋 НАСТРОЙКИ ДЛЯ РЕАЛИЗАЦИИ:')
            print(f'• Лимит rate limiter: {rpm * 0.9:.0f} запросов/минуту')
            print(f'• Макс скорость диалога: {rpm/60*0.8:.1f} запросов/секунду')
            print(f'• Рекомендуемая задержка: {60/(rpm*0.8):.1f} секунд между запросами')
    
    else:
        print('\n❌ НЕТ ДОСТУПНЫХ МОДЕЛЕЙ!')
        print('Проверьте API ключ и подключение к интернету.')

if __name__ == "__main__":
    test_models()
