#!/usr/bin/env python3
"""
Daily Meeting Emulator - Main Entry Point

AI-powered simulation tool for generating realistic daily standup meetings
using Google Gemini Flash 2.5 for comprehensive testing of off-topic detection.
"""

import os
import sys
import click
from pathlib import Path
from dotenv import load_dotenv
from rich.console import Console

# Add src directory to Python path for imports
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))
from rich.panel import Panel
from rich.text import Text

# Load environment variables
load_dotenv()

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

console = Console()


def display_banner():
    """Display application banner"""
    banner = Text("Daily Meeting Emulator", style="bold blue")
    subtitle = Text("AI-powered standup meeting simulation", style="italic")
    
    panel_content = Text()
    panel_content.append(banner)
    panel_content.append("\n")
    panel_content.append(subtitle)
    panel_content.append("\n\n")
    panel_content.append("🤖 Powered by Google Gemini 2.5 Flash", style="green")
    
    console.print(Panel(panel_content, title="🎭 Meeting Simulator", border_style="blue"))


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """Daily Meeting Emulator - Generate realistic standup meeting transcripts"""
    display_banner()


@cli.command()
@click.option('--config', '-c', default='config/default_meeting.yaml',
              help='Configuration file path')
@click.option('--output', '-o', default=None,
              help='Output file path (auto-generated if not specified)')
@click.option('--participants', '-p', type=int, default=None,
              help='Number of participants (overrides config)')
@click.option('--duration', '-d', type=int, default=None,
              help='Meeting duration in minutes (overrides config)')
@click.option('--style', '-s', type=click.Choice(['strict', 'relaxed', 'chaotic']),
              default=None, help='Meeting style (overrides config)')
@click.option('--off-topic-ratio', '-r', type=float, default=None,
              help='Off-topic content ratio 0.0-1.0 (overrides config)')
@click.option('--real-time/--fast', default=True,
              help='Real-time speech simulation vs fast generation (default: real-time)')
@click.option('--voice', is_flag=True, default=False,
              help='Enable Deepgram voice generation (creates audio files)')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def generate(config, output, participants, duration, style, off_topic_ratio, real_time, voice, verbose):
    """Generate a single meeting transcript"""
    
    try:
        # Check for API key
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            console.print("❌ [red]GEMINI_API_KEY not found in environment variables[/red]")
            console.print("💡 [yellow]Please set your Gemini API key in .env file[/yellow]")
            console.print("🔗 Get free API key at: https://makersuite.google.com/app/apikey")
            sys.exit(1)
        
        # Import after API key check
        from meeting.meeting_simulator import MeetingSimulator
        from config.config_loader import ConfigLoader
        
        # Load configuration
        console.print(f"📄 Loading configuration: {config}")
        config_loader = ConfigLoader()
        meeting_config = config_loader.load_config(config)
        
        # Apply CLI overrides
        if participants:
            meeting_config['meeting']['participant_count'] = participants
        if duration:
            meeting_config['meeting']['duration_minutes'] = duration
        if style:
            meeting_config['meeting']['style'] = style
        if off_topic_ratio:
            meeting_config['content']['off_topic_ratio'] = off_topic_ratio
        
        if verbose:
            console.print("🔧 [blue]Configuration loaded:[/blue]")
            console.print(f"   • Participants: {meeting_config['meeting']['participant_count']}")
            console.print(f"   • Duration: {meeting_config['meeting']['duration_minutes']} minutes")
            console.print(f"   • Style: {meeting_config['meeting']['style']}")
            console.print(f"   • Off-topic ratio: {meeting_config['content']['off_topic_ratio']}")
            console.print(f"   • Real-time simulation: {real_time}")
            console.print(f"   • Voice generation: {voice}")

        # Check voice requirements
        if voice:
            deepgram_key = os.getenv('DEEPGRAM_API_KEY')
            if not deepgram_key:
                console.print("❌ [red]DEEPGRAM_API_KEY not found for voice generation[/red]")
                console.print("💡 [yellow]Please set your Deepgram API key in .env file or disable --voice[/yellow]")
                console.print("🔗 Get free API key at: https://console.deepgram.com/")
                sys.exit(1)

        # Initialize simulator
        console.print("🤖 [green]Initializing meeting simulator...[/green]")
        if real_time:
            console.print("⏱️  [yellow]Real-time mode: Speech will be simulated at natural pace[/yellow]")
        else:
            console.print("⚡ [yellow]Fast mode: Instant generation without speech delays[/yellow]")

        if voice:
            console.print("🎙️ [blue]Voice mode: Audio files will be generated[/blue]")

        simulator = MeetingSimulator(meeting_config, api_key, real_time=real_time, enable_voice=voice)
        
        # Generate meeting
        console.print("🎭 [yellow]Generating meeting transcript...[/yellow]")
        console.print("💬 [cyan]AI-generated dialogue will appear below:[/cyan]\n")

        transcript = simulator.simulate_meeting()
        
        # Save output
        if not output:
            timestamp = transcript.metadata.get('timestamp', 'unknown')
            output = f"data/generated/meeting_{timestamp}.txt"
        
        # Ensure output directory exists
        Path(output).parent.mkdir(parents=True, exist_ok=True)
        
        # Save transcript
        with open(output, 'w', encoding='utf-8') as f:
            f.write(transcript.to_text())
        
        console.print(f"✅ [green]Meeting transcript generated: {output}[/green]")
        
        # Display summary
        console.print("\n📊 [blue]Meeting Summary:[/blue]")
        console.print(f"   • Duration: {transcript.duration:.1f} minutes")
        console.print(f"   • Participants: {len(transcript.participants)}")
        console.print(f"   • Total segments: {len(transcript.segments)}")
        console.print(f"   • Off-topic segments: {transcript.off_topic_count}")
        console.print(f"   • Quality score: {transcript.quality_score:.2f}")
        
    except Exception as e:
        console.print(f"❌ [red]Error: {e}[/red]")
        if verbose:
            import traceback
            console.print(traceback.format_exc())
        sys.exit(1)


@cli.command()
@click.option('--config', '-c', default='config/default_meeting.yaml',
              help='Configuration file path')
@click.option('--count', '-n', type=int, default=10,
              help='Number of meetings to generate')
@click.option('--output-dir', '-o', default='data/generated/batch',
              help='Output directory for batch generation')
@click.option('--parallel', '-j', type=int, default=1,
              help='Number of parallel processes')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def batch(config, count, output_dir, parallel, verbose):
    """Generate multiple meeting transcripts in batch"""
    
    try:
        # Check for API key
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            console.print("❌ [red]GEMINI_API_KEY not found[/red]")
            sys.exit(1)
        
        from meeting.meeting_simulator import MeetingSimulator
        from config.config_loader import ConfigLoader
        import time
        from concurrent.futures import ThreadPoolExecutor
        
        console.print(f"🏭 [blue]Starting batch generation: {count} meetings[/blue]")
        
        # Load configuration
        config_loader = ConfigLoader()
        meeting_config = config_loader.load_config(config)
        
        # Ensure output directory exists
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        def generate_single_meeting(index):
            """Generate a single meeting for batch processing"""
            try:
                simulator = MeetingSimulator(meeting_config, api_key)
                transcript = simulator.simulate_meeting()
                
                output_file = Path(output_dir) / f"meeting_{index:03d}.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(transcript.to_text())
                
                return f"✅ Generated: {output_file}"
            except Exception as e:
                return f"❌ Failed {index}: {e}"
        
        # Generate meetings
        start_time = time.time()
        
        if parallel > 1:
            with ThreadPoolExecutor(max_workers=parallel) as executor:
                results = list(executor.map(generate_single_meeting, range(count)))
        else:
            results = []
            for i in range(count):
                if verbose:
                    console.print(f"Generating meeting {i+1}/{count}...")
                results.append(generate_single_meeting(i))
        
        # Display results
        generation_time = time.time() - start_time
        successful = sum(1 for r in results if r.startswith("✅"))
        
        console.print(f"\n📊 [blue]Batch Generation Complete:[/blue]")
        console.print(f"   • Total meetings: {count}")
        console.print(f"   • Successful: {successful}")
        console.print(f"   • Failed: {count - successful}")
        console.print(f"   • Generation time: {generation_time:.1f}s")
        console.print(f"   • Average time per meeting: {generation_time/count:.1f}s")
        console.print(f"   • Output directory: {output_dir}")
        
        if verbose:
            for result in results:
                console.print(f"   {result}")
        
    except Exception as e:
        console.print(f"❌ [red]Batch generation failed: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--config', '-c', default='config/default_meeting.yaml',
              help='Configuration file to validate')
def validate(config):
    """Validate configuration file"""
    
    try:
        from config.config_loader import ConfigLoader
        
        console.print(f"🔍 [blue]Validating configuration: {config}[/blue]")
        
        config_loader = ConfigLoader()
        meeting_config = config_loader.load_config(config)
        
        # Perform validation
        is_valid, errors = config_loader.validate_config(meeting_config)
        
        if is_valid:
            console.print("✅ [green]Configuration is valid![/green]")
            
            # Display configuration summary
            console.print("\n📋 [blue]Configuration Summary:[/blue]")
            console.print(f"   • Participants: {meeting_config['meeting']['participant_count']}")
            console.print(f"   • Duration: {meeting_config['meeting']['duration_minutes']} minutes")
            console.print(f"   • Style: {meeting_config['meeting']['style']}")
            console.print(f"   • Off-topic ratio: {meeting_config['content']['off_topic_ratio']}")
        else:
            console.print("❌ [red]Configuration validation failed:[/red]")
            for error in errors:
                console.print(f"   • {error}")
            sys.exit(1)
        
    except Exception as e:
        console.print(f"❌ [red]Validation error: {e}[/red]")
        sys.exit(1)


@cli.command()
def examples():
    """Show example configurations and usage"""
    
    console.print("📚 [blue]Daily Meeting Emulator - Examples[/blue]\n")
    
    console.print("🎯 [yellow]Basic Usage:[/yellow]")
    console.print("   python main.py generate")
    console.print("   python main.py generate --participants 6 --duration 20")
    console.print("   python main.py generate --style chaotic --off-topic-ratio 0.4\n")
    
    console.print("🏭 [yellow]Batch Generation:[/yellow]")
    console.print("   python main.py batch --count 50")
    console.print("   python main.py batch --count 100 --parallel 4\n")
    
    console.print("⚙️ [yellow]Custom Configuration:[/yellow]")
    console.print("   python main.py generate --config config/scenarios/strict_standup.yaml")
    console.print("   python main.py validate --config config/personalities/rambler.yaml\n")
    
    console.print("🔧 [yellow]Available Configurations:[/yellow]")
    
    # List available configurations
    config_dir = Path("config")
    if config_dir.exists():
        for config_file in config_dir.rglob("*.yaml"):
            console.print(f"   • {config_file}")


if __name__ == "__main__":
    cli()
