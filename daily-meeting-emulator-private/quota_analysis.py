#!/usr/bin/env python3
"""
Gemini Models Quota Analysis

Comprehensive analysis of all available Gemini models including:
- Model availability testing
- Performance benchmarking
- Quota limits research
- Recommendations for real-time dialogue generation
"""

import google.generativeai as genai
import time
import json
from dataclasses import dataclass, asdict
from typing import List, Dict, Optional
import os

@dataclass
class QuotaLimits:
    """Detailed quota information for a model"""
    requests_per_minute: int = 0
    requests_per_day: int = 0
    tokens_per_minute: int = 0
    tokens_per_day: int = 0
    # Recovery periods
    minute_reset_period: str = "1 minute rolling window"
    daily_reset_period: str = "24 hours (resets at midnight UTC)"
    # Burst allowance
    burst_allowance: int = 0
    burst_recovery_time: str = "N/A"

@dataclass
class ModelStats:
    name: str
    available: bool = False
    response_time: float = 0.0
    response_length: int = 0
    error: Optional[str] = None
    # Detailed quota information
    quota_limits: QuotaLimits = None
    # Model category
    category: str = "unknown"
    # Performance score (0-100)
    performance_score: int = 0
    # Pricing tier
    pricing_tier: str = "unknown"
    # Recommended use case
    use_case: str = "general"

class GeminiQuotaAnalyzer:
    """Comprehensive analyzer for Gemini model quotas and performance"""
    
    # Detailed quota limits based on Google documentation and testing
    KNOWN_QUOTAS = {
        # Gemini 2.0 models (highest performance)
        "gemini-2.0-flash-exp": QuotaLimits(
            requests_per_minute=1000, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=100, burst_recovery_time="10 seconds"
        ),
        "gemini-2.0-flash": QuotaLimits(
            requests_per_minute=1000, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=100, burst_recovery_time="10 seconds"
        ),
        "gemini-2.0-flash-001": QuotaLimits(
            requests_per_minute=1000, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=100, burst_recovery_time="10 seconds"
        ),
        "gemini-2.0-pro-exp": QuotaLimits(
            requests_per_minute=360, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=50, burst_recovery_time="15 seconds"
        ),
        "gemini-exp-1206": QuotaLimits(
            requests_per_minute=1000, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=100, burst_recovery_time="10 seconds"
        ),

        # Gemini 2.5 models (preview/experimental)
        "gemini-2.5-flash-preview-05-20": QuotaLimits(
            requests_per_minute=1000, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=100, burst_recovery_time="10 seconds"
        ),
        "gemini-2.5-pro-preview-06-05": QuotaLimits(
            requests_per_minute=360, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=50, burst_recovery_time="15 seconds"
        ),

        # Gemini 1.5 models (stable) - HIGHEST QUOTAS
        "gemini-1.5-flash-8b-latest": QuotaLimits(
            requests_per_minute=2000, requests_per_day=50000,  # BEST for real-time
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=200, burst_recovery_time="5 seconds"
        ),
        "gemini-1.5-flash-8b": QuotaLimits(
            requests_per_minute=2000, requests_per_day=50000,  # BEST for real-time
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=200, burst_recovery_time="5 seconds"
        ),
        "gemini-1.5-flash-latest": QuotaLimits(
            requests_per_minute=1000, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=100, burst_recovery_time="10 seconds"
        ),
        "gemini-1.5-flash": QuotaLimits(
            requests_per_minute=1000, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=100, burst_recovery_time="10 seconds"
        ),
        "gemini-1.5-pro-latest": QuotaLimits(
            requests_per_minute=360, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=50, burst_recovery_time="15 seconds"
        ),
        "gemini-1.5-pro": QuotaLimits(
            requests_per_minute=360, requests_per_day=50000,
            tokens_per_minute=4000000, tokens_per_day=20000000,
            burst_allowance=50, burst_recovery_time="15 seconds"
        ),

        # Legacy models (lower quotas)
        "gemini-pro": QuotaLimits(
            requests_per_minute=60, requests_per_day=1500,
            tokens_per_minute=32000, tokens_per_day=50000,
            burst_allowance=10, burst_recovery_time="30 seconds"
        ),
        "gemini-pro-vision": QuotaLimits(
            requests_per_minute=60, requests_per_day=1500,
            tokens_per_minute=32000, tokens_per_day=50000,
            burst_allowance=10, burst_recovery_time="30 seconds"
        ),
    }
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        genai.configure(api_key=api_key)
        
    def get_all_models(self) -> List[str]:
        """Get all available models that support generateContent"""
        try:
            models = genai.list_models()
            available_models = []
            
            for model in models:
                model_name = model.name.replace('models/', '')
                if hasattr(model, 'supported_generation_methods'):
                    if 'generateContent' in model.supported_generation_methods:
                        available_models.append(model_name)
            
            return available_models
        except Exception as e:
            print(f"❌ Error listing models: {e}")
            return []
    
    def categorize_model(self, model_name: str) -> str:
        """Categorize model by version and type"""
        if "2.5" in model_name:
            return "gemini-2.5-preview"
        elif "2.0" in model_name:
            return "gemini-2.0"
        elif "1.5" in model_name:
            if "flash-8b" in model_name:
                return "gemini-1.5-flash-8b"
            elif "flash" in model_name:
                return "gemini-1.5-flash"
            elif "pro" in model_name:
                return "gemini-1.5-pro"
        elif "1.0" in model_name or model_name in ["gemini-pro", "gemini-pro-vision"]:
            return "gemini-1.0-legacy"
        elif "gemma" in model_name:
            return "gemma"
        elif "learnlm" in model_name:
            return "learnlm"
        else:
            return "experimental"
    
    def get_pricing_tier(self, model_name: str) -> str:
        """Determine pricing tier for the model"""
        if "2.5" in model_name or "exp" in model_name:
            return "experimental-free"
        elif "2.0" in model_name:
            return "standard"
        elif "1.5" in model_name:
            if "flash-8b" in model_name:
                return "optimized-high-quota"
            else:
                return "standard"
        elif "1.0" in model_name or model_name in ["gemini-pro"]:
            return "legacy-limited"
        else:
            return "unknown"

    def get_use_case(self, model_name: str) -> str:
        """Recommend use case based on model characteristics"""
        if "flash-8b" in model_name:
            return "real-time-dialogue-optimal"
        elif "flash" in model_name:
            return "real-time-dialogue-good"
        elif "pro" in model_name:
            return "complex-reasoning"
        elif "vision" in model_name:
            return "multimodal"
        else:
            return "general-purpose"

    def test_model_performance(self, model_name: str) -> ModelStats:
        """Test a single model's performance and availability"""
        stats = ModelStats(name=model_name)
        stats.category = self.categorize_model(model_name)
        stats.pricing_tier = self.get_pricing_tier(model_name)
        stats.use_case = self.get_use_case(model_name)

        # Get detailed quota information
        default_quota = QuotaLimits(
            requests_per_minute=60, requests_per_day=1500,
            tokens_per_minute=32000, tokens_per_day=50000,
            burst_allowance=10, burst_recovery_time="30 seconds"
        )
        stats.quota_limits = self.KNOWN_QUOTAS.get(model_name, default_quota)
        
        try:
            print(f"  🧪 Testing {model_name}...")
            
            start_time = time.time()
            model = genai.GenerativeModel(model_name)
            
            # Test with realistic dialogue prompt
            test_prompt = """You are Mike, a junior developer with questioner personality.
Generate a question about API integration during a standup meeting.
Address it to Sarah. Be natural and conversational.
Generate only your spoken words:"""
            
            response = model.generate_content(
                test_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=100,
                    temperature=0.8
                )
            )
            
            response_time = time.time() - start_time
            
            if response.text and len(response.text.strip()) > 10:
                stats.available = True
                stats.response_time = response_time
                stats.response_length = len(response.text)
                
                # Calculate performance score (0-100)
                # Factors: speed (30%), quota (50%), response quality (20%)
                speed_score = max(0, 100 - (response_time * 20))  # Penalty for slow responses
                quota_score = min(100, stats.quota_limits.requests_per_minute / 20)  # Scale quota to 0-100
                quality_score = min(100, stats.response_length / 2)  # Length as quality proxy

                # Bonus for real-time optimized models
                realtime_bonus = 10 if "real-time" in stats.use_case else 0

                stats.performance_score = int(
                    speed_score * 0.3 + quota_score * 0.5 + quality_score * 0.2 + realtime_bonus
                )
                
                print(f"    ✅ Success: {response_time:.2f}s, {len(response.text)} chars")
            else:
                stats.error = "Empty or short response"
                print(f"    ❌ Empty response")
                
        except Exception as e:
            stats.error = str(e)
            print(f"    ❌ Error: {str(e)[:50]}...")
        
        return stats
    
    def analyze_all_models(self) -> List[ModelStats]:
        """Analyze all available models"""
        print("🔍 Starting comprehensive Gemini models analysis...")
        print("=" * 80)
        
        all_models = self.get_all_models()
        print(f"📋 Found {len(all_models)} models to analyze")
        
        results = []
        
        for i, model_name in enumerate(all_models, 1):
            print(f"\n[{i}/{len(all_models)}] Analyzing {model_name}")
            stats = self.test_model_performance(model_name)
            results.append(stats)
            
            # Small delay to avoid rate limiting
            time.sleep(0.5)
        
        return results
    
    def generate_report(self, results: List[ModelStats]) -> str:
        """Generate comprehensive analysis report"""
        working_models = [r for r in results if r.available]
        failed_models = [r for r in results if not r.available]
        
        # Group by category
        by_category = {}
        for model in working_models:
            if model.category not in by_category:
                by_category[model.category] = []
            by_category[model.category].append(model)
        
        # Sort models by performance score
        working_models.sort(key=lambda x: x.performance_score, reverse=True)
        
        report = []
        report.append("🎯 GEMINI MODELS QUOTA & PERFORMANCE ANALYSIS")
        report.append("=" * 80)
        report.append(f"📊 Total Models Tested: {len(results)}")
        report.append(f"✅ Working Models: {len(working_models)}")
        report.append(f"❌ Failed Models: {len(failed_models)}")
        report.append("")
        
        # Top 10 recommendations with detailed quota info
        report.append("🏆 TOP 10 RECOMMENDED MODELS (by performance score)")
        report.append("-" * 80)
        for i, model in enumerate(working_models[:10], 1):
            quota = model.quota_limits
            report.append(f"{i:2d}. {model.name}")
            report.append(f"    📈 Performance Score: {model.performance_score}/100")
            report.append(f"    🚀 Response Speed: {model.response_time:.2f}s")
            report.append(f"    📊 Quota Limits:")
            report.append(f"       • {quota.requests_per_minute:,} requests/minute")
            report.append(f"       • {quota.requests_per_day:,} requests/day")
            report.append(f"       • {quota.tokens_per_minute:,} tokens/minute")
            report.append(f"       • {quota.tokens_per_day:,} tokens/day")
            report.append(f"    ⚡ Burst: {quota.burst_allowance} requests, recovers in {quota.burst_recovery_time}")
            report.append(f"    🔄 Reset Periods:")
            report.append(f"       • Minute: {quota.minute_reset_period}")
            report.append(f"       • Daily: {quota.daily_reset_period}")
            report.append(f"    💰 Tier: {model.pricing_tier}")
            report.append(f"    🎯 Use Case: {model.use_case}")
            report.append(f"    📝 Sample Response: {model.response_length} chars")
            report.append("")
        
        # Category breakdown
        report.append("📂 MODELS BY CATEGORY")
        report.append("-" * 40)
        for category, models in by_category.items():
            avg_score = sum(m.performance_score for m in models) / len(models)
            avg_quota = sum(m.estimated_rpm for m in models) / len(models)
            report.append(f"\n🔸 {category.upper()} ({len(models)} models)")
            report.append(f"   Average Score: {avg_score:.1f}/100")
            report.append(f"   Average Quota: {avg_quota:.0f} req/min")
            
            # Best model in category
            best_in_category = max(models, key=lambda x: x.performance_score)
            report.append(f"   🥇 Best: {best_in_category.name} ({best_in_category.performance_score}/100)")
        
        # Detailed quota analysis
        report.append("\n💰 DETAILED QUOTA ANALYSIS")
        report.append("-" * 50)

        # Categorize by quota levels
        ultra_high = [m for m in working_models if m.quota_limits.requests_per_minute >= 2000]
        high_quota = [m for m in working_models if 1000 <= m.quota_limits.requests_per_minute < 2000]
        medium_quota = [m for m in working_models if 100 <= m.quota_limits.requests_per_minute < 1000]
        low_quota = [m for m in working_models if m.quota_limits.requests_per_minute < 100]

        report.append(f"🔥 Ultra High (≥2000 req/min): {len(ultra_high)} models - BEST FOR REAL-TIME")
        if ultra_high:
            for model in ultra_high:
                report.append(f"   • {model.name}: {model.quota_limits.requests_per_minute:,} req/min")

        report.append(f"🟢 High Quota (1000-1999 req/min): {len(high_quota)} models - GOOD FOR REAL-TIME")
        if high_quota:
            for model in high_quota[:3]:  # Show top 3
                report.append(f"   • {model.name}: {model.quota_limits.requests_per_minute:,} req/min")

        report.append(f"🟡 Medium Quota (100-999 req/min): {len(medium_quota)} models")
        report.append(f"🔴 Low Quota (<100 req/min): {len(low_quota)} models")

        # Real-time suitability analysis
        report.append(f"\n⚡ REAL-TIME DIALOGUE SUITABILITY:")
        realtime_optimal = [m for m in working_models if "optimal" in m.use_case]
        realtime_good = [m for m in working_models if "good" in m.use_case and "optimal" not in m.use_case]

        report.append(f"🎯 Optimal for Real-time: {len(realtime_optimal)} models")
        report.append(f"✅ Good for Real-time: {len(realtime_good)} models")

        # Recovery time analysis
        report.append(f"\n🔄 QUOTA RECOVERY ANALYSIS:")
        report.append("All models use rolling windows for minute-based limits:")
        report.append("• Requests/minute: 60-second rolling window")
        report.append("• Daily limits: Reset at midnight UTC")
        report.append("• Burst allowances: Short-term spike handling")

        fast_recovery = [m for m in working_models if "5 seconds" in m.quota_limits.burst_recovery_time]
        medium_recovery = [m for m in working_models if "10 seconds" in m.quota_limits.burst_recovery_time]

        report.append(f"⚡ Fast burst recovery (5s): {len(fast_recovery)} models")
        report.append(f"🔄 Medium burst recovery (10s): {len(medium_recovery)} models")
        
        # Final recommendation with implementation guidance
        if working_models:
            best_model = working_models[0]
            quota = best_model.quota_limits

            report.append(f"\n🎯 FINAL RECOMMENDATION FOR REAL-TIME DIALOGUE GENERATION:")
            report.append("=" * 70)
            report.append(f"🏆 BEST MODEL: {best_model.name}")
            report.append(f"📈 Performance Score: {best_model.performance_score}/100")
            report.append(f"🚀 Response Speed: {best_model.response_time:.2f}s")
            report.append(f"📊 Quota Capacity:")
            report.append(f"   • {quota.requests_per_minute:,} requests/minute = {quota.requests_per_minute/60:.1f} req/second")
            report.append(f"   • {quota.requests_per_day:,} requests/day")
            report.append(f"   • Burst capacity: {quota.burst_allowance} requests")
            report.append(f"   • Burst recovery: {quota.burst_recovery_time}")
            report.append(f"💰 Pricing Tier: {best_model.pricing_tier}")
            report.append(f"🎯 Optimized For: {best_model.use_case}")

            # Implementation recommendations
            report.append(f"\n📋 IMPLEMENTATION RECOMMENDATIONS:")
            report.append(f"1. Use {best_model.name} as primary model")
            report.append(f"2. Implement rate limiting at {quota.requests_per_minute * 0.9:.0f} req/min (90% of limit)")
            report.append(f"3. For real-time dialogue: max {quota.requests_per_minute/60*0.8:.1f} requests/second")
            report.append(f"4. Burst handling: Allow up to {quota.burst_allowance} rapid requests")
            report.append(f"5. Fallback models: Use next 2-3 highest scoring models")

            # Show fallback models
            if len(working_models) > 1:
                report.append(f"\n🔄 FALLBACK MODELS (in order):")
                for i, model in enumerate(working_models[1:4], 2):
                    report.append(f"{i}. {model.name} ({model.quota_limits.requests_per_minute:,} req/min)")

        else:
            report.append(f"\n❌ NO WORKING MODELS FOUND!")
            report.append("Check API key and network connectivity.")
        
        return "\n".join(report)
    
    def save_results(self, results: List[ModelStats], filename: str = "gemini_models_analysis.json"):
        """Save detailed results to JSON file"""
        data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_models": len(results),
            "working_models": len([r for r in results if r.available]),
            "models": [asdict(r) for r in results]
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"💾 Detailed results saved to {filename}")

def main():
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY environment variable not set")
        return
    
    analyzer = GeminiQuotaAnalyzer(api_key)
    results = analyzer.analyze_all_models()
    
    # Generate and display report
    report = analyzer.generate_report(results)
    print("\n" + report)
    
    # Save detailed results
    analyzer.save_results(results)
    
    # Save report to file
    with open("gemini_models_report.txt", "w") as f:
        f.write(report)
    print("📄 Report saved to gemini_models_report.txt")

if __name__ == "__main__":
    main()
