# Default Meeting Configuration
# Daily Meeting Emulator - Standard Standup Settings

meeting:
  # Basic meeting parameters
  duration_minutes: 15
  participant_count: 5
  style: "mixed"  # strict, relaxed, chaotic, mixed
  
  # Meeting structure
  phases:
    opening_duration: 0.1    # 10% of total time
    updates_duration: 0.7    # 70% of total time
    blockers_duration: 0.15  # 15% of total time
    closing_duration: 0.05   # 5% of total time

# Participant configuration
participants:
  - name: "<PERSON>"
    role: "scrum_master"
    personality: "facilitator"
    traits:
      verbosity: 0.6
      technical_detail: 0.3
      interruption_tendency: 0.2
      curiosity: 0.5
      off_topic_probability: 0.1
      time_awareness: 0.9
  
  - name: "<PERSON>"
    role: "senior_developer"
    personality: "rambler"
    traits:
      verbosity: 0.8
      technical_detail: 0.9
      interruption_tendency: 0.3
      curiosity: 0.6
      off_topic_probability: 0.4
      time_awareness: 0.4
  
  - name: "Mike"
    role: "junior_developer"
    personality: "questioner"
    traits:
      verbosity: 0.6
      technical_detail: 0.5
      interruption_tendency: 0.7
      curiosity: 0.9
      off_topic_probability: 0.3
      time_awareness: 0.6
  
  - name: "<PERSON>"
    role: "devops_engineer"
    personality: "minimalist"
    traits:
      verbosity: 0.3
      technical_detail: 0.6
      interruption_tendency: 0.1
      curiosity: 0.4
      off_topic_probability: 0.2
      time_awareness: 0.8
  
  - name: "<PERSON>"
    role: "qa_engineer"
    personality: "problem_solver"
    traits:
      verbosity: 0.7
      technical_detail: 0.7
      interruption_tendency: 0.4
      curiosity: 0.7
      off_topic_probability: 0.3
      time_awareness: 0.5

# Content generation settings
content:
  # Target ratios for content types
  off_topic_ratio: 0.3  # 30% off-topic content
  
  # Content categories and their weights
  content_types:
    accomplishments: 0.4    # Yesterday's work
    plans: 0.3             # Today's plans
    blockers: 0.2          # Impediments
    questions: 0.1         # Clarifications
  
  # Off-topic content types
  off_topic_types:
    technical_detail: 0.4   # Excessive technical explanations
    problem_solving: 0.3    # Trying to solve problems in standup
    meta_discussion: 0.2    # Process/meeting discussions
    lengthy_excuses: 0.1    # Long explanations for delays

# Dialogue generation settings
dialogue:
  # Gemini API configuration
  gemini:
    temperature: 0.8        # Creativity level
    max_tokens: 500         # Maximum response length
    top_p: 0.9             # Nucleus sampling
    top_k: 40              # Top-k sampling
  
  # Response timing
  timing:
    base_speaking_speed: 150  # Words per minute
    pause_factor: 1.2         # Multiplier for pauses
    interruption_delay: 0.5   # Seconds before interruption
  
  # Context management
  context:
    max_history_length: 10    # Number of previous statements to remember
    topic_memory_length: 20   # Number of topics to track

# Output settings
output:
  # Transcript format
  format: "timestamp"  # timestamp, simple, detailed
  
  # Include metadata
  include_metadata: true
  
  # File naming
  filename_template: "meeting_{timestamp}_{style}_{duration}min"
  
  # Quality metrics
  generate_quality_score: true
  include_analysis_tags: true  # Mark on-topic/off-topic content

# Bot configuration
bots:
  # Off-topic monitor bot
  off_topic_monitor:
    enabled: true
    sensitivity: 0.7  # Confidence threshold for intervention (0.0-1.0)
    interruption_style: "gentle"  # gentle, direct, formal
    max_interventions_per_meeting: 3
    respect_hierarchy: true
    intervention_delay: 1.0  # seconds to wait before intervening
    categories:
      - technical_deep_dive
      - problem_solving
      - architecture_discussion
      - debugging_stories
      - process_discussion

# Advanced settings
advanced:
  # Interruption behavior
  interruptions:
    enabled: true
    max_per_participant: 3
    probability_decay: 0.8  # Reduce probability over time

  # Natural conversation flow
  conversation:
    enable_follow_ups: true
    question_response_probability: 0.7
    topic_continuation_probability: 0.4

  # Meeting dynamics
  dynamics:
    energy_level: "medium"  # low, medium, high
    formality_level: "casual"  # formal, casual, mixed
    team_cohesion: "good"   # poor, average, good, excellent
