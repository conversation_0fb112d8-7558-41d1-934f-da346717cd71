#!/usr/bin/env python3
"""
БЫСТРЫЙ ТЕСТ ВСЕХ GEMINI МОДЕЛЕЙ
Проверяет доступность без зависаний на восстановлении квот
"""

import google.generativeai as genai
import time
import json
import os
from datetime import datetime
from dataclasses import dataclass, asdict

@dataclass
class ModelResult:
    name: str
    available: bool = False
    response_time: float = 0.0
    response_length: int = 0
    error_type: str = ""
    error_message: str = ""
    estimated_rpm: int = 0
    category: str = ""
    recommendation: str = ""

def quick_test_all_models():
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY не установлен")
        return
    
    genai.configure(api_key=api_key)
    
    # ВСЕ 31 МОДЕЛЬ ИЗ СПИСКА
    all_models = [
        'gemini-1.0-pro-vision-latest',
        'gemini-pro-vision',
        'gemini-1.5-pro-latest',
        'gemini-1.5-pro-002',
        'gemini-1.5-pro',
        'gemini-1.5-flash-latest',
        'gemini-1.5-flash',
        'gemini-1.5-flash-002',
        'gemini-1.5-flash-8b',
        'gemini-1.5-flash-8b-001',
        'gemini-1.5-flash-8b-latest',
        'gemini-2.5-pro-exp-03-25',
        'gemini-2.5-pro-preview-03-25',
        'gemini-2.5-flash-preview-04-17',
        'gemini-2.5-flash-preview-05-20',
        'gemini-2.5-flash-preview-04-17-thinking',
        'gemini-2.5-pro-preview-05-06',
        'gemini-2.5-pro-preview-06-05',
        'gemini-2.0-flash-exp',
        'gemini-2.0-flash',
        'gemini-2.0-flash-001',
        'gemini-2.0-flash-lite-001',
        'gemini-2.0-flash-lite',
        'gemini-2.0-flash-lite-preview-02-05',
        'gemini-2.0-flash-lite-preview',
        'gemini-2.0-pro-exp',
        'gemini-2.0-pro-exp-02-05',
        'gemini-exp-1206',
        'gemini-2.0-flash-thinking-exp-01-21',
        'gemini-2.0-flash-thinking-exp',
        'gemini-2.0-flash-thinking-exp-1219'
    ]
    
    # Известные характеристики моделей с информацией о восстановлении квот
    model_specs = {
        # Gemini 1.0 (устаревшие) - квота восстанавливается каждую минуту
        'gemini-1.0-pro-vision-latest': {'rpm': 60, 'cat': 'legacy-vision', 'rec': 'УСТАРЕЛА', 'recovery': '60s rolling'},
        'gemini-pro-vision': {'rpm': 60, 'cat': 'legacy-vision', 'rec': 'УСТАРЕЛА', 'recovery': '60s rolling'},
        
        # Gemini 1.5 Pro (средние квоты) - квота восстанавливается каждую минуту
        'gemini-1.5-pro-latest': {'rpm': 360, 'cat': 'pro-1.5', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
        'gemini-1.5-pro-002': {'rpm': 360, 'cat': 'pro-1.5', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
        'gemini-1.5-pro': {'rpm': 360, 'cat': 'pro-1.5', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
        
        # Gemini 1.5 Flash (хорошие квоты) - быстрое восстановление
        'gemini-1.5-flash-latest': {'rpm': 1000, 'cat': 'flash-1.5', 'rec': 'ХОРОШО', 'recovery': '60s rolling'},
        'gemini-1.5-flash': {'rpm': 1000, 'cat': 'flash-1.5', 'rec': 'ХОРОШО', 'recovery': '60s rolling'},
        'gemini-1.5-flash-002': {'rpm': 1000, 'cat': 'flash-1.5', 'rec': 'ХОРОШО', 'recovery': '60s rolling'},
        
        # Gemini 1.5 Flash 8B (ЛУЧШИЕ квоты) - самое быстрое восстановление
        'gemini-1.5-flash-8b': {'rpm': 2000, 'cat': 'flash-8b', 'rec': '🔥 ЛУЧШИЙ', 'recovery': '60s rolling'},
        'gemini-1.5-flash-8b-001': {'rpm': 2000, 'cat': 'flash-8b', 'rec': '🔥 ЛУЧШИЙ', 'recovery': '60s rolling'},
        'gemini-1.5-flash-8b-latest': {'rpm': 2000, 'cat': 'flash-8b', 'rec': '🔥 ЛУЧШИЙ', 'recovery': '60s rolling'},
        
        # Gemini 2.0 Flash - отличные квоты
        'gemini-2.0-flash-exp': {'rpm': 1000, 'cat': 'flash-2.0', 'rec': 'ОТЛИЧНО', 'recovery': '60s rolling'},
        'gemini-2.0-flash': {'rpm': 1000, 'cat': 'flash-2.0', 'rec': 'ОТЛИЧНО', 'recovery': '60s rolling'},
        'gemini-2.0-flash-001': {'rpm': 1000, 'cat': 'flash-2.0', 'rec': 'ОТЛИЧНО', 'recovery': '60s rolling'},
        'gemini-exp-1206': {'rpm': 1000, 'cat': 'experimental', 'rec': 'ОТЛИЧНО', 'recovery': '60s rolling'},
        
        # Gemini 2.0 Lite - быстрые модели
        'gemini-2.0-flash-lite-001': {'rpm': 1000, 'cat': 'lite-2.0', 'rec': 'БЫСТРО', 'recovery': '60s rolling'},
        'gemini-2.0-flash-lite': {'rpm': 1000, 'cat': 'lite-2.0', 'rec': 'БЫСТРО', 'recovery': '60s rolling'},
        'gemini-2.0-flash-lite-preview-02-05': {'rpm': 1000, 'cat': 'lite-2.0', 'rec': 'БЫСТРО', 'recovery': '60s rolling'},
        'gemini-2.0-flash-lite-preview': {'rpm': 1000, 'cat': 'lite-2.0', 'rec': 'БЫСТРО', 'recovery': '60s rolling'},
        
        # Gemini 2.0 Pro - средние квоты
        'gemini-2.0-pro-exp': {'rpm': 360, 'cat': 'pro-2.0', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
        'gemini-2.0-pro-exp-02-05': {'rpm': 360, 'cat': 'pro-2.0', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
        
        # Gemini 2.5 - новейшие модели
        'gemini-2.5-pro-exp-03-25': {'rpm': 360, 'cat': 'pro-2.5', 'rec': 'НОВЫЙ', 'recovery': '60s rolling'},
        'gemini-2.5-pro-preview-03-25': {'rpm': 360, 'cat': 'pro-2.5', 'rec': 'НОВЫЙ', 'recovery': '60s rolling'},
        'gemini-2.5-flash-preview-04-17': {'rpm': 1000, 'cat': 'flash-2.5', 'rec': 'НОВЫЙ', 'recovery': '60s rolling'},
        'gemini-2.5-flash-preview-05-20': {'rpm': 1000, 'cat': 'flash-2.5', 'rec': 'НОВЫЙ', 'recovery': '60s rolling'},
        'gemini-2.5-pro-preview-05-06': {'rpm': 360, 'cat': 'pro-2.5', 'rec': 'НОВЫЙ', 'recovery': '60s rolling'},
        'gemini-2.5-pro-preview-06-05': {'rpm': 360, 'cat': 'pro-2.5', 'rec': 'НОВЫЙ', 'recovery': '60s rolling'},
        
        # Thinking модели - специальные
        'gemini-2.0-flash-thinking-exp-01-21': {'rpm': 500, 'cat': 'thinking', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
        'gemini-2.0-flash-thinking-exp': {'rpm': 500, 'cat': 'thinking', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
        'gemini-2.0-flash-thinking-exp-1219': {'rpm': 500, 'cat': 'thinking', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
        'gemini-2.5-flash-preview-04-17-thinking': {'rpm': 500, 'cat': 'thinking', 'rec': 'МЕДЛЕННО', 'recovery': '60s rolling'},
    }
    
    print('🔍 БЫСТРЫЙ ТЕСТ ВСЕХ GEMINI МОДЕЛЕЙ')
    print('=' * 80)
    print(f'📋 Всего моделей: {len(all_models)}')
    print(f'⏰ Время начала: {datetime.now().strftime("%H:%M:%S")}')
    print()
    
    results = []
    
    for i, model_name in enumerate(all_models, 1):
        print(f'[{i:2d}/{len(all_models)}] {model_name}...', end=' ')
        
        result = ModelResult(name=model_name)
        specs = model_specs.get(model_name, {'rpm': 60, 'cat': 'unknown', 'rec': 'НЕИЗВЕСТНО', 'recovery': 'unknown'})
        result.estimated_rpm = specs['rpm']
        result.category = specs['cat']
        result.recommendation = specs['rec']
        
        try:
            start_time = time.time()
            model = genai.GenerativeModel(model_name)
            
            # Быстрый тест
            response = model.generate_content(
                'Hi',
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=5,
                    temperature=0.1
                )
            )
            
            response_time = time.time() - start_time
            
            # Проверяем ответ
            response_text = ""
            try:
                response_text = response.text if response.text else ""
            except Exception:
                # Для thinking моделей
                try:
                    response_text = str(response.candidates[0].content.parts[0].text) if response.candidates else "ok"
                except:
                    response_text = "response_ok"
            
            if len(response_text.strip()) >= 0:  # Принимаем любой ответ
                result.available = True
                result.response_time = response_time
                result.response_length = len(response_text)
                print(f'✅ {response_time:.2f}s ({specs["rpm"]} req/min)')
            else:
                result.error_type = "empty_response"
                result.error_message = "Пустой ответ"
                print(f'❌ Пустой ответ')
                
        except Exception as e:
            error_msg = str(e)
            result.error_message = error_msg
            
            if '429' in error_msg or 'quota' in error_msg.lower():
                result.error_type = "quota_exceeded"
                print(f'⚠️  КВОТА ПРЕВЫШЕНА')
            elif '404' in error_msg or 'not found' in error_msg.lower():
                result.error_type = "not_found"
                print(f'🗑️  НЕДОСТУПНА')
            elif 'overloaded' in error_msg.lower():
                result.error_type = "overloaded"
                print(f'🔥 ПЕРЕГРУЖЕНА')
            elif 'response.text' in error_msg:
                result.error_type = "response_format"
                print(f'⚙️  ФОРМАТ ОТВЕТА')
            else:
                result.error_type = "other_error"
                print(f'❌ ОШИБКА')
        
        results.append(result)
        time.sleep(0.1)  # Короткая задержка
    
    # Генерируем отчет
    generate_quick_report(results, model_specs)

def generate_quick_report(results, model_specs):
    """Генерирует быстрый отчет"""
    working_models = [r for r in results if r.available]
    quota_exceeded = [r for r in results if r.error_type == "quota_exceeded"]
    not_found = [r for r in results if r.error_type == "not_found"]
    
    print('\n' + '='*80)
    print('📊 ИТОГОВЫЙ ОТЧЕТ ПО ВСЕМ GEMINI МОДЕЛЯМ')
    print('='*80)
    
    print(f'✅ Работающих моделей: {len(working_models)}')
    print(f'⚠️  Квота превышена: {len(quota_exceeded)}')
    print(f'🗑️  Недоступных: {len(not_found)}')
    print(f'❌ Других ошибок: {len(results) - len(working_models) - len(quota_exceeded) - len(not_found)}')
    print()
    
    if working_models:
        print('🏆 ТОП РАБОТАЮЩИХ МОДЕЛЕЙ ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ:')
        print('-' * 70)
        
        # Сортируем по квоте и скорости
        working_models.sort(key=lambda x: (x.estimated_rpm, -x.response_time), reverse=True)
        
        for i, model in enumerate(working_models[:10], 1):
            specs = model_specs.get(model.name, {})
            recovery = specs.get('recovery', 'unknown')
            
            print(f'{i:2d}. {model.name}')
            print(f'    📊 {model.estimated_rpm:,} req/min = {model.estimated_rpm/60:.1f} req/sec')
            print(f'    ⚡ {model.response_time:.2f}s | 🎯 {model.recommendation}')
            print(f'    🔄 Восстановление квоты: {recovery}')
            print()
    
    # Анализ по категориям
    categories = {}
    for model in working_models:
        cat = model.category
        if cat not in categories:
            categories[cat] = []
        categories[cat].append(model)
    
    print('📂 РАБОТАЮЩИЕ МОДЕЛИ ПО КАТЕГОРИЯМ:')
    print('-' * 50)
    
    for category, models in categories.items():
        avg_rpm = sum(m.estimated_rpm for m in models) / len(models)
        print(f'\n🔸 {category.upper()} ({len(models)} моделей)')
        print(f'   Средняя квота: {avg_rpm:.0f} req/min')
        
        best_in_cat = max(models, key=lambda x: x.estimated_rpm)
        print(f'   🥇 Лучшая: {best_in_cat.name} ({best_in_cat.estimated_rpm} req/min)')
    
    # Информация о восстановлении квот
    print(f'\n🔄 ИНФОРМАЦИЯ О ВОССТАНОВЛЕНИИ КВОТ:')
    print('-' * 50)
    print('📋 ВСЕ GEMINI МОДЕЛИ ИСПОЛЬЗУЮТ ОДИНАКОВУЮ СИСТЕМУ:')
    print('• Минутные лимиты: Скользящее окно 60 секунд')
    print('• Дневные лимиты: Сброс в полночь UTC')
    print('• Восстановление: Постепенное в течение минуты')
    print('• Burst запросы: Кратковременные всплески разрешены')
    print()
    print('⏰ ПЕРИОДЫ ВОССТАНОВЛЕНИЯ:')
    print('• Каждую секунду восстанавливается 1/60 от минутного лимита')
    print('• Например, для 2000 req/min восстанавливается ~33 запроса/секунду')
    print('• Для 1000 req/min восстанавливается ~17 запросов/секунду')
    print('• Для 360 req/min восстанавливается ~6 запросов/секунду')
    
    # Финальная рекомендация
    if working_models:
        best = working_models[0]
        print(f'\n🎯 ИТОГОВАЯ РЕКОМЕНДАЦИЯ ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ:')
        print('='*60)
        print(f'🏆 ЛУЧШАЯ МОДЕЛЬ: {best.name}')
        print(f'📊 Квота: {best.estimated_rpm:,} запросов/минуту')
        print(f'⚡ Скорость: {best.response_time:.2f} секунды')
        print(f'🎯 Оценка: {best.recommendation}')
        print(f'💡 Для диалога: до {best.estimated_rpm/60*0.8:.1f} запросов/секунду (80% лимита)')
        print(f'🛡️  Безопасный лимит: {best.estimated_rpm*0.9:.0f} запросов/минуту (90% лимита)')
        print(f'🔄 Восстановление: {best.estimated_rpm/60:.1f} запросов каждую секунду')
    
    # Сохраняем результаты
    save_results(results)

def save_results(results):
    """Сохраняет результаты"""
    data = {
        'timestamp': datetime.now().isoformat(),
        'total_tested': len(results),
        'working_count': len([r for r in results if r.available]),
        'models': [asdict(r) for r in results]
    }
    
    filename = f'gemini_models_quick_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f'\n💾 Результаты сохранены в {filename}')

if __name__ == "__main__":
    quick_test_all_models()
