# Off-Topic Monitor Bot - Requirements and Design Plan (RDP)

## 📋 **Project Overview**
Design and implement an AI-powered bot participant that monitors daily standup meetings in real-time, detects off-topic conversations using Gemini 2.5 Flash, and gently interrupts to suggest establishing separate meetings for off-topic discussions.

## 🎯 **Requirements**

### **Functional Requirements**

#### **FR-1: Real-Time Conversation Monitoring**
- **FR-1.1**: <PERSON><PERSON> must listen to all conversation segments in real-time
- **FR-1.2**: <PERSON><PERSON> must maintain conversation context and history
- **FR-1.3**: <PERSON><PERSON> must track current meeting phase (opening, updates, blockers, closing)
- **FR-1.4**: <PERSON><PERSON> must identify speaker and content for each segment

#### **FR-2: Off-Topic Detection**
- **FR-2.1**: Use Gemini 2.5 Flash model for content analysis
- **FR-2.2**: Classify conversations as on-topic vs off-topic for standup meetings
- **FR-2.3**: Detect specific off-topic categories:
  - Technical deep-dives during updates
  - Problem-solving discussions
  - Architecture debates
  - Lengthy debugging stories
  - Process discussions
  - Personal conversations
- **FR-2.4**: Maintain confidence scores for off-topic detection
- **FR-2.5**: Consider meeting phase context (some topics appropriate in certain phases)

#### **FR-3: Intelligent Interruption**
- **FR-3.1**: Interrupt only when confidence threshold is exceeded (>0.7)
- **FR-3.2**: Use gentle, professional language for interruptions
- **FR-3.3**: Suggest specific actions (separate meeting, parking lot, follow-up)
- **FR-3.4**: Avoid interrupting the same topic multiple times
- **FR-3.5**: Respect speaker hierarchy (be more careful with senior roles)

#### **FR-4: Meeting Integration**
- **FR-4.1**: Integrate seamlessly with existing meeting flow
- **FR-4.2**: Participate as a regular meeting participant
- **FR-4.3**: Maintain meeting momentum and energy
- **FR-4.4**: Support both real-time and fast generation modes

### **Non-Functional Requirements**

#### **NFR-1: Performance**
- **NFR-1.1**: Analysis response time < 2 seconds per segment
- **NFR-1.2**: Minimal impact on meeting generation speed
- **NFR-1.3**: Efficient API usage (respect Gemini quotas)

#### **NFR-2: Reliability**
- **NFR-2.1**: Graceful degradation if AI analysis fails
- **NFR-2.2**: Fallback to rule-based detection if needed
- **NFR-2.3**: Error handling for API failures

#### **NFR-3: Configurability**
- **NFR-3.1**: Configurable sensitivity levels
- **NFR-3.2**: Customizable interruption phrases
- **NFR-3.3**: Enable/disable bot functionality
- **NFR-3.4**: Configurable off-topic categories

## 🏗️ **System Design**

### **Architecture Overview**
```
┌─────────────────────────────────────────────────────────────┐
│                    Meeting Simulator                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Regular       │  │   Regular       │  │  Off-Topic      │ │
│  │ Participant 1   │  │ Participant 2   │  │ Monitor Bot     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │        │
│           └─────────────────────┼─────────────────────┘        │
│                                 │                              │
│  ┌─────────────────────────────────────────────────────────────┤
│  │              Conversation Flow                              │
│  │  1. Participant speaks                                      │
│  │  2. Bot analyzes content                                    │
│  │  3. Bot decides whether to interrupt                       │
│  │  4. Bot posts intervention if needed                       │
│  └─────────────────────────────────────────────────────────────┤
└─────────────────────────────────────────────────────────────┘
```

### **Component Design**

#### **1. Base Bot Interface** (`src/bots/base_bot.py`)
```python
class BaseBot(BaseParticipant):
    """Base class for all meeting bots"""
    
    def __init__(self, name: str, config: Dict[str, Any])
    def process_conversation_segment(self, segment: TranscriptSegment) -> Optional[str]
    def should_interrupt(self, context: ConversationContext) -> bool
    def generate_intervention(self, context: ConversationContext) -> str
```

#### **2. Off-Topic Monitor Bot** (`src/bots/off_topic_monitor_bot.py`)
```python
class OffTopicMonitorBot(BaseBot):
    """AI-powered bot that monitors for off-topic conversations"""
    
    def __init__(self, config: Dict[str, Any], gemini_api_key: str)
    def analyze_content_with_ai(self, content: str, context: str) -> OffTopicAnalysis
    def classify_off_topic_type(self, content: str) -> str
    def generate_gentle_interruption(self, analysis: OffTopicAnalysis) -> str
    def suggest_alternative_action(self, off_topic_type: str) -> str
```

#### **3. Analysis Data Structures**
```python
@dataclass
class OffTopicAnalysis:
    is_off_topic: bool
    confidence: float
    category: str
    reasoning: str
    suggested_action: str

@dataclass
class InterventionDecision:
    should_interrupt: bool
    intervention_text: str
    timing: str  # "immediate", "after_speaker", "end_of_phase"
```

### **Integration Points**

#### **Meeting Simulator Integration**
1. **Bot Registration**: Add bot to participant list
2. **Segment Processing**: Call bot after each conversation segment
3. **Interruption Handling**: Insert bot responses when triggered
4. **Context Sharing**: Provide conversation history to bot

#### **Configuration Integration**
```yaml
# config/default_meeting.yaml
bots:
  off_topic_monitor:
    enabled: true
    sensitivity: 0.7  # Confidence threshold
    interruption_style: "gentle"  # gentle, direct, formal
    max_interruptions_per_meeting: 3
    respect_hierarchy: true
    categories:
      - technical_deep_dive
      - problem_solving
      - architecture_discussion
      - debugging_stories
      - process_discussion
```

## 🔧 **Implementation Plan**

### **Phase 1: Core Bot Infrastructure**
1. Create `src/bots/` module structure
2. Implement `BaseBot` abstract class
3. Create bot configuration system
4. Add bot integration to meeting simulator

### **Phase 2: AI Analysis Engine**
1. Implement Gemini 2.0 Flash integration for content analysis
2. Create off-topic detection prompts
3. Implement confidence scoring
4. Add category classification

### **Phase 3: Interruption Logic**
1. Implement intelligent interruption timing
2. Create gentle interruption phrase generation
3. Add suggestion system for alternative actions
4. Implement interruption frequency limits

### **Phase 4: Integration & Testing**
1. Integrate bot with existing meeting flow
2. Add configuration options
3. Test with various meeting scenarios
4. Optimize performance and reliability

## 📝 **Detailed Specifications**

### **Off-Topic Categories**
1. **Technical Deep-Dive**: Excessive implementation details during updates
2. **Problem Solving**: Attempting to solve complex issues in standup
3. **Architecture Discussion**: High-level design conversations
4. **Debugging Stories**: Lengthy explanations of bug investigation
5. **Process Discussion**: Talking about meeting processes or methodologies
6. **Personal Conversation**: Non-work related discussions

### **Interruption Phrases**
- **Gentle**: "I think this might be a great topic for a separate discussion..."
- **Professional**: "This sounds important - should we schedule a focused session?"
- **Collaborative**: "Let's capture this for a follow-up meeting so we can give it proper attention..."

### **Timing Strategies**
- **Immediate**: Interrupt during long monologues (>30 seconds off-topic)
- **After Speaker**: Wait for natural pause, then intervene
- **End of Phase**: Summarize off-topic items at phase transitions

## 🧪 **Testing Strategy**

### **Unit Tests**
- Off-topic detection accuracy
- Interruption timing logic
- Configuration handling
- Error scenarios

### **Integration Tests**
- Bot participation in meetings
- Real-time analysis performance
- Meeting flow preservation

### **Scenario Tests**
- Various off-topic conversation types
- Different meeting phases
- Multiple interruptions handling
- Edge cases and error conditions

## 📊 **Success Metrics**
- **Detection Accuracy**: >85% correct off-topic identification
- **False Positive Rate**: <15% incorrect interruptions
- **Meeting Flow Impact**: <10% increase in meeting duration
- **User Acceptance**: Interruptions feel natural and helpful

## 🔄 **Future Enhancements**
- Learning from meeting outcomes
- Personalized interruption styles
- Integration with calendar systems
- Meeting effectiveness scoring
- Multi-language support

---

**Document Version**: 1.0  
**Created**: 2025-06-17  
**Author**: AI Assistant  
**Status**: Draft - Ready for Implementation
