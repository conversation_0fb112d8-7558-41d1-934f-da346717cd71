#!/usr/bin/env python3
"""
ТЕСТ ВОССТАНОВЛЕНИЯ КВОТ GEMINI МОДЕЛЕЙ
Проверяет все модели и измеряет реальное время восстановления квот
"""

import google.generativeai as genai
import time
import json
import os
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import List, Dict, Optional

@dataclass
class QuotaRecoveryResult:
    model_name: str
    available: bool = False
    response_time: float = 0.0
    quota_hit: bool = False
    recovery_time: Optional[float] = None
    requests_before_limit: int = 0
    error_message: str = ""
    estimated_rpm: int = 0
    category: str = ""

def test_quota_recovery():
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY не установлен")
        return
    
    genai.configure(api_key=api_key)
    
    # ВСЕ МОДЕЛИ ИЗ СПИСКА (31 модель)
    all_models = [
        'gemini-1.0-pro-vision-latest',
        'gemini-pro-vision',
        'gemini-1.5-pro-latest',
        'gemini-1.5-pro-002',
        'gemini-1.5-pro',
        'gemini-1.5-flash-latest',
        'gemini-1.5-flash',
        'gemini-1.5-flash-002',
        'gemini-1.5-flash-8b',
        'gemini-1.5-flash-8b-001',
        'gemini-1.5-flash-8b-latest',
        'gemini-2.5-pro-exp-03-25',
        'gemini-2.5-pro-preview-03-25',
        'gemini-2.5-flash-preview-04-17',
        'gemini-2.5-flash-preview-05-20',
        'gemini-2.5-flash-preview-04-17-thinking',
        'gemini-2.5-pro-preview-05-06',
        'gemini-2.5-pro-preview-06-05',
        'gemini-2.0-flash-exp',
        'gemini-2.0-flash',
        'gemini-2.0-flash-001',
        'gemini-2.0-flash-lite-001',
        'gemini-2.0-flash-lite',
        'gemini-2.0-flash-lite-preview-02-05',
        'gemini-2.0-flash-lite-preview',
        'gemini-2.0-pro-exp',
        'gemini-2.0-pro-exp-02-05',
        'gemini-exp-1206',
        'gemini-2.0-flash-thinking-exp-01-21',
        'gemini-2.0-flash-thinking-exp',
        'gemini-2.0-flash-thinking-exp-1219'
    ]
    
    # Известные характеристики моделей
    model_specs = {
        # Gemini 1.0 (устаревшие)
        'gemini-1.0-pro-vision-latest': {'rpm': 60, 'cat': 'legacy-vision'},
        'gemini-pro-vision': {'rpm': 60, 'cat': 'legacy-vision'},
        
        # Gemini 1.5 Pro (средние квоты)
        'gemini-1.5-pro-latest': {'rpm': 360, 'cat': 'pro-1.5'},
        'gemini-1.5-pro-002': {'rpm': 360, 'cat': 'pro-1.5'},
        'gemini-1.5-pro': {'rpm': 360, 'cat': 'pro-1.5'},
        
        # Gemini 1.5 Flash (хорошие квоты)
        'gemini-1.5-flash-latest': {'rpm': 1000, 'cat': 'flash-1.5'},
        'gemini-1.5-flash': {'rpm': 1000, 'cat': 'flash-1.5'},
        'gemini-1.5-flash-002': {'rpm': 1000, 'cat': 'flash-1.5'},
        
        # Gemini 1.5 Flash 8B (ЛУЧШИЕ квоты)
        'gemini-1.5-flash-8b': {'rpm': 2000, 'cat': 'flash-8b'},
        'gemini-1.5-flash-8b-001': {'rpm': 2000, 'cat': 'flash-8b'},
        'gemini-1.5-flash-8b-latest': {'rpm': 2000, 'cat': 'flash-8b'},
        
        # Gemini 2.0 Flash
        'gemini-2.0-flash-exp': {'rpm': 1000, 'cat': 'flash-2.0'},
        'gemini-2.0-flash': {'rpm': 1000, 'cat': 'flash-2.0'},
        'gemini-2.0-flash-001': {'rpm': 1000, 'cat': 'flash-2.0'},
        'gemini-exp-1206': {'rpm': 1000, 'cat': 'experimental'},
        
        # Gemini 2.0 Lite
        'gemini-2.0-flash-lite-001': {'rpm': 1000, 'cat': 'lite-2.0'},
        'gemini-2.0-flash-lite': {'rpm': 1000, 'cat': 'lite-2.0'},
        'gemini-2.0-flash-lite-preview-02-05': {'rpm': 1000, 'cat': 'lite-2.0'},
        'gemini-2.0-flash-lite-preview': {'rpm': 1000, 'cat': 'lite-2.0'},
        
        # Gemini 2.0 Pro
        'gemini-2.0-pro-exp': {'rpm': 360, 'cat': 'pro-2.0'},
        'gemini-2.0-pro-exp-02-05': {'rpm': 360, 'cat': 'pro-2.0'},
        
        # Gemini 2.5
        'gemini-2.5-pro-exp-03-25': {'rpm': 360, 'cat': 'pro-2.5'},
        'gemini-2.5-pro-preview-03-25': {'rpm': 360, 'cat': 'pro-2.5'},
        'gemini-2.5-flash-preview-04-17': {'rpm': 1000, 'cat': 'flash-2.5'},
        'gemini-2.5-flash-preview-05-20': {'rpm': 1000, 'cat': 'flash-2.5'},
        'gemini-2.5-pro-preview-05-06': {'rpm': 360, 'cat': 'pro-2.5'},
        'gemini-2.5-pro-preview-06-05': {'rpm': 360, 'cat': 'pro-2.5'},
        
        # Thinking модели
        'gemini-2.0-flash-thinking-exp-01-21': {'rpm': 500, 'cat': 'thinking'},
        'gemini-2.0-flash-thinking-exp': {'rpm': 500, 'cat': 'thinking'},
        'gemini-2.0-flash-thinking-exp-1219': {'rpm': 500, 'cat': 'thinking'},
        'gemini-2.5-flash-preview-04-17-thinking': {'rpm': 500, 'cat': 'thinking'},
    }
    
    print('🔍 ТЕСТ ВОССТАНОВЛЕНИЯ КВОТ GEMINI МОДЕЛЕЙ')
    print('=' * 80)
    print(f'📋 Всего моделей для тестирования: {len(all_models)}')
    print(f'⏰ Время начала: {datetime.now().strftime("%H:%M:%S")}')
    print()
    
    results = []
    
    for i, model_name in enumerate(all_models, 1):
        print(f'[{i:2d}/{len(all_models)}] Тестируем {model_name}...')
        
        result = QuotaRecoveryResult(model_name=model_name)
        specs = model_specs.get(model_name, {'rpm': 60, 'cat': 'unknown'})
        result.estimated_rpm = specs['rpm']
        result.category = specs['cat']
        
        # Тест доступности модели
        try:
            start_time = time.time()
            model = genai.GenerativeModel(model_name)
            
            # Простой тест
            response = model.generate_content(
                'Hi',
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=10,
                    temperature=0.1
                )
            )
            
            response_time = time.time() - start_time
            
            # Проверяем ответ
            response_text = ""
            try:
                response_text = response.text if response.text else ""
            except Exception as e:
                if "thinking" in model_name.lower():
                    # Для thinking моделей пробуем другой способ
                    try:
                        response_text = str(response.candidates[0].content.parts[0].text) if response.candidates else ""
                    except:
                        response_text = "thinking_model_response"
                else:
                    raise e
            
            if len(response_text.strip()) > 0:
                result.available = True
                result.response_time = response_time
                
                print(f'   ✅ РАБОТАЕТ: {response_time:.2f}s')
                print(f'   📊 Ожидаемая квота: {specs["rpm"]} req/min')
                print(f'   📝 Ответ: "{response_text[:30]}..."')
                
                # Быстрый тест на превышение квоты (только для быстрых моделей)
                if specs['rpm'] >= 1000:
                    print(f'   🧪 Быстрый тест лимитов (5 запросов)...')
                    quota_test_result = test_quota_limits_fast(model, model_name)
                    result.quota_hit = quota_test_result['hit']
                    result.recovery_time = quota_test_result['recovery_time']
                    result.requests_before_limit = quota_test_result['requests']

                    if result.quota_hit:
                        print(f'   ⚠️  Квота достигнута после {result.requests_before_limit} запросов')
                        if result.recovery_time:
                            print(f'   🔄 Время восстановления: {result.recovery_time:.1f} секунд')
                    else:
                        print(f'   ✅ Квота не достигнута за {result.requests_before_limit} запросов')
                
            else:
                result.error_message = "Пустой ответ"
                print(f'   ❌ Пустой ответ')
                
        except Exception as e:
            error_msg = str(e)
            result.error_message = error_msg
            
            if '429' in error_msg or 'quota' in error_msg.lower():
                print(f'   ⚠️  КВОТА УЖЕ ПРЕВЫШЕНА')
                result.quota_hit = True
                
                # Быстрая проверка восстановления (максимум 30 секунд)
                print(f'   🔄 Быстрая проверка восстановления (30с)...')
                recovery_time = test_recovery_time_fast(model_name)
                result.recovery_time = recovery_time
                if recovery_time:
                    print(f'   ⏰ Время восстановления: {recovery_time:.1f} секунд')
                else:
                    print(f'   ❓ Квота не восстановилась за 30 секунд')
                    
            elif '404' in error_msg or 'not found' in error_msg.lower():
                print(f'   🗑️  МОДЕЛЬ НЕДОСТУПНА/УСТАРЕЛА')
            elif 'overloaded' in error_msg.lower():
                print(f'   🔥 МОДЕЛЬ ПЕРЕГРУЖЕНА')
            else:
                print(f'   ❌ ОШИБКА: {error_msg[:50]}...')
        
        results.append(result)
        print()
        
        # Небольшая задержка между тестами
        time.sleep(1)
    
    # Генерируем отчет
    generate_quota_report(results)
    
    # Сохраняем результаты
    save_quota_results(results)

def test_quota_limits_fast(model, model_name, max_requests=5):
    """Быстрый тест лимитов квоты для модели (максимум 5 запросов)"""
    result = {'hit': False, 'recovery_time': None, 'requests': 0}

    for i in range(max_requests):
        try:
            response = model.generate_content(
                f'Test {i+1}',
                generation_config=genai.types.GenerationConfig(max_output_tokens=3)
            )
            result['requests'] = i + 1
            time.sleep(0.05)  # Очень короткая задержка

        except Exception as e:
            if '429' in str(e) or 'quota' in str(e).lower():
                result['hit'] = True
                result['requests'] = i

                # Быстрый тест восстановления
                recovery_time = test_recovery_time_fast(model_name)
                result['recovery_time'] = recovery_time
                break
            else:
                break

    return result

def test_recovery_time_fast(model_name, max_wait=30):
    """Быстрый тест времени восстановления квоты (максимум 30 секунд)"""
    start_time = time.time()

    # Проверяем каждые 5 секунд
    check_intervals = [5, 10, 15, 20, 25, 30]

    for wait_time in check_intervals:
        if time.time() - start_time >= wait_time:
            try:
                model = genai.GenerativeModel(model_name)
                response = model.generate_content(
                    'Recovery test',
                    generation_config=genai.types.GenerationConfig(max_output_tokens=3)
                )

                # Если запрос прошел, квота восстановилась
                recovery_time = time.time() - start_time
                return recovery_time

            except Exception as e:
                if '429' in str(e) or 'quota' in str(e).lower():
                    # Квота еще не восстановилась, продолжаем ждать
                    if wait_time < 30:
                        time.sleep(5)
                        continue
                    else:
                        return None  # Не восстановилась за 30 секунд
                else:
                    # Другая ошибка
                    return None
        else:
            time.sleep(wait_time - (time.time() - start_time))

    return None  # Не восстановилась за 30 секунд

def generate_quota_report(results):
    """Генерирует отчет по квотам"""
    working_models = [r for r in results if r.available]
    quota_hit_models = [r for r in results if r.quota_hit]
    
    print('📊 ОТЧЕТ ПО КВОТАМ И ВРЕМЕНИ ВОССТАНОВЛЕНИЯ')
    print('=' * 80)
    
    print(f'✅ Работающих моделей: {len(working_models)}')
    print(f'⚠️  Моделей с превышением квоты: {len(quota_hit_models)}')
    print()
    
    # Группировка по категориям
    categories = {}
    for model in working_models:
        cat = model.category
        if cat not in categories:
            categories[cat] = []
        categories[cat].append(model)
    
    print('📂 РАБОТАЮЩИЕ МОДЕЛИ ПО КАТЕГОРИЯМ:')
    print('-' * 60)
    
    for category, models in categories.items():
        avg_rpm = sum(m.estimated_rpm for m in models) / len(models)
        avg_time = sum(m.response_time for m in models) / len(models)
        
        print(f'\n🔸 {category.upper()} ({len(models)} моделей)')
        print(f'   Средняя квота: {avg_rpm:.0f} req/min')
        print(f'   Средняя скорость: {avg_time:.2f}s')
        
        for model in sorted(models, key=lambda x: x.estimated_rpm, reverse=True):
            recovery_info = ""
            if model.recovery_time:
                recovery_info = f" | Восстановление: {model.recovery_time:.1f}s"
            elif model.quota_hit:
                recovery_info = " | Квота превышена"
            
            print(f'   • {model.name}: {model.estimated_rpm} req/min, {model.response_time:.2f}s{recovery_info}')
    
    # Анализ времени восстановления
    recovery_models = [r for r in results if r.recovery_time is not None]
    if recovery_models:
        print(f'\n🔄 АНАЛИЗ ВРЕМЕНИ ВОССТАНОВЛЕНИЯ КВОТ:')
        print('-' * 50)
        
        avg_recovery = sum(m.recovery_time for m in recovery_models) / len(recovery_models)
        min_recovery = min(m.recovery_time for m in recovery_models)
        max_recovery = max(m.recovery_time for m in recovery_models)
        
        print(f'📊 Статистика восстановления ({len(recovery_models)} моделей):')
        print(f'   • Среднее время: {avg_recovery:.1f} секунд')
        print(f'   • Минимальное: {min_recovery:.1f} секунд')
        print(f'   • Максимальное: {max_recovery:.1f} секунд')
        print()
        
        print('🔄 ДЕТАЛИ ПО ВОССТАНОВЛЕНИЮ:')
        for model in sorted(recovery_models, key=lambda x: x.recovery_time):
            print(f'   • {model.name}: {model.recovery_time:.1f}s')
    
    # Финальные рекомендации
    if working_models:
        best_models = sorted(working_models, key=lambda x: (x.estimated_rpm, -x.response_time), reverse=True)[:3]
        
        print(f'\n🎯 ТОП-3 РЕКОМЕНДАЦИИ:')
        print('=' * 50)
        
        for i, model in enumerate(best_models, 1):
            print(f'{i}. {model.name}')
            print(f'   📊 {model.estimated_rpm:,} req/min = {model.estimated_rpm/60:.1f} req/sec')
            print(f'   ⚡ {model.response_time:.2f}s ответ')
            if model.recovery_time:
                print(f'   🔄 {model.recovery_time:.1f}s восстановление')
            print()

def save_quota_results(results):
    """Сохраняет результаты в файл"""
    data = {
        'timestamp': datetime.now().isoformat(),
        'total_tested': len(results),
        'working_count': len([r for r in results if r.available]),
        'quota_hit_count': len([r for r in results if r.quota_hit]),
        'models': [asdict(r) for r in results]
    }
    
    filename = f'quota_recovery_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f'💾 Результаты сохранены в {filename}')

if __name__ == "__main__":
    test_quota_recovery()
