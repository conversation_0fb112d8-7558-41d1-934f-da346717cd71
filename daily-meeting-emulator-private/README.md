# Daily Meeting Emulator 🎭

**AI-powered simulation tool for generating realistic daily standup meetings with voice synthesis**

Generate high-quality, contextual meeting transcripts and audio with diverse participant personalities using Google Gemini 2.5 Flash for dialogue generation and Deepgram Aura 2 for voice synthesis.

## ✨ Features

### 🤖 **AI-Powered Dialogue Generation**
- **Realistic Conversations**: Natural standup meeting dialogues using Google Gemini 2.5 Flash
- **Contextual Responses**: Intelligent participant interactions, questions, and blockers
- **Quality Scoring**: Built-in metrics for dialogue realism (typically 0.8-0.9)
- **Dynamic Content**: Adaptive conversation flow based on participant personalities

### 🎙️ **Advanced Voice Synthesis**
- **Deepgram Aura 2**: High-quality text-to-speech with SSML enhancements
- **Personality Voices**: Different voices for each participant type
- **Real-time Playback**: Live audio generation during meeting simulation
- **Audio Export**: Individual WAV files and complete meeting playlists
- **Natural Speech**: Breathing patterns, pauses, and emphasis

### 🎭 **Participant Personalities**
- **Facilitator**: Clear, authoritative meeting leadership (`aura-asteria-en`)
- **Rambler**: Detailed, lengthy explanations with tangents (`aura-luna-en`)
- **Questioner**: Curious, engaging with lots of questions (`aura-stella-en`)
- **Minimalist**: Concise, direct communication (`aura-zeus-en`)
- **Problem Solver**: Solution-focused, confident responses (`aura-hera-en`)
- **Storyteller**: Narrative, expressive communication (`aura-orion-en`)
- **Interrupter**: Quick, energetic interjections (`aura-arcas-en`)

### 🤖 **AI Meeting Bot**
- **Off-Topic Monitor**: Uses Gemini 2.0 Flash to detect off-topic conversations
- **Gentle Interruptions**: Professionally suggests separate meetings for off-topic items
- **Smart Analysis**: Identifies technical deep-dives, problem-solving, and architecture discussions
- **Configurable Sensitivity**: Adjustable intervention thresholds and styles

### ⚙️ **Configurable Parameters**
- **Meeting Duration**: 1-30 minutes with realistic timing
- **Participant Count**: 2-8 participants with balanced interactions
- **Content Style**: Professional, casual, or mixed tones
- **Off-topic Ratio**: Control tangential discussions (0-100%)
- **Voice Generation**: Optional high-quality audio synthesis

## 🚀 Quick Start

### 1. **Installation**
```bash
# Clone the repository
git clone https://github.com/your-username/daily-meeting-emulator-private.git
cd daily-meeting-emulator-private

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# or
.venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### 2. **API Keys Setup**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file and add your API keys:
```

**Required:**
- **Gemini API Key**: Get free key at [Google AI Studio](https://makersuite.google.com/app/apikey)
  ```
  GEMINI_API_KEY=your_gemini_api_key_here
  ```

**Optional (for voice generation):**
- **Deepgram API Key**: Get free key at [Deepgram Console](https://console.deepgram.com/)
  ```
  DEEPGRAM_API_KEY=your_deepgram_api_key_here
  ```

### 3. **Basic Usage**
```bash
# Generate a simple text meeting
python main.py generate --participants 3 --duration 5

# Generate with voice synthesis
python main.py generate --participants 2 --duration 3 --voice

# Generate with off-topic monitoring bot
python main.py generate --participants 4 --duration 5 --bots

# Fast generation (no speech timing, no bots)
python main.py generate --participants 4 --duration 2 --fast --no-bots

# Custom configuration with bot monitoring
python main.py generate --participants 6 --duration 10 --style casual --off-topic-ratio 0.2
```

## 📋 Command Line Options

```bash
python main.py generate [OPTIONS]

Options:
  --participants INTEGER    Number of meeting participants (2-8) [default: 3]
  --duration INTEGER       Meeting duration in minutes (1-30) [default: 5]
  --style TEXT            Meeting style: professional, casual, mixed [default: professional]
  --off-topic-ratio FLOAT Off-topic content ratio (0.0-1.0) [default: 0.1]
  --real-time / --fast    Real-time speech simulation vs fast generation [default: real-time]
  --voice                 Enable Deepgram voice generation (creates audio files)
  --bots / --no-bots      Enable meeting bots (off-topic monitor, etc.) [default: enabled]
  --verbose, -v           Verbose output with detailed information
  --config TEXT           Custom configuration file path [default: config/default_meeting.yaml]
  --output TEXT           Output directory for generated files [default: data/generated]
  --help                  Show this message and exit
```

## 📁 Output Files

### **Text Transcripts**
- **Location**: `data/generated/`
- **Format**: `meeting_YYYYMMDD_HHMMSS.txt`
- **Content**: Complete meeting transcript with timestamps and participant info

### **Audio Files** (when `--voice` enabled)
- **Location**: `data/audio/`
- **Individual Segments**: `YYYYMMDD_HHMMSS_segment_XXX_ParticipantName.wav`
- **Playlist**: `YYYYMMDD_HHMMSS_playlist.txt`
- **Quality**: 24kHz, 16-bit WAV format

## 🎯 Example Output

```
🎭 Alex (facilitator):
   💬 Good morning, everyone. Let's begin our daily stand-up. We'll keep it concise to respect everyone's time.

🎭 Sarah (rambler):
   💬 Yesterday I completed the code review for the notification service. Today I'm working on performance optimizations...

🤖 OffTopicMonitor (bot):
   💬 Excuse me, I think this might be a great topic for a separate discussion. Let's schedule a technical deep-dive session to explore this properly.

📊 Meeting Summary:
   • Duration: 3.2 minutes
   • Participants: 3
   • Total segments: 8
   • Bot interventions: 1
   • Off-topic segments: 1
   • Quality score: 0.87
```

## 🛠️ Development Status

### ✅ **Completed Features**
- [x] **AI Dialogue Generation** - Google Gemini 2.5 Flash integration
- [x] **Voice Synthesis** - Deepgram Aura 2 with SSML enhancements
- [x] **Participant Personalities** - 7 distinct personality types
- [x] **Off-Topic Monitor Bot** - Gemini 2.0 Flash powered meeting facilitation
- [x] **Real-time Simulation** - Natural speech timing and pauses
- [x] **Audio Export** - WAV files and playlists
- [x] **Rich Console Interface** - Beautiful terminal output
- [x] **Configuration System** - YAML-based settings
- [x] **Quality Metrics** - Dialogue realism scoring
- [x] **CLI Interface** - Comprehensive command-line options

### 🎯 **Quality Metrics**
- **Dialogue Realism**: Typically 0.8-0.9 (excellent)
- **Voice Quality**: 24kHz professional audio
- **Generation Speed**: ~30 seconds for 5-minute meeting
- **API Efficiency**: Optimized quota usage with fallback models

## 🔧 Technical Details

### **Dependencies**
- **Python**: 3.12+ (async/await support)
- **Google Gemini**: 2.5 Flash model for dialogue generation
- **Deepgram**: Aura 2 voices for speech synthesis
- **Rich**: Terminal interface and formatting
- **PyYAML**: Configuration management
- **Pydub**: Audio processing (optional)

### **Architecture**
```
src/
├── meeting/           # Core meeting simulation
├── participants/      # Personality implementations
├── dialogue/          # Gemini AI integration
├── voice/            # Deepgram voice synthesis
├── output/           # Transcript formatting
├── config/           # Configuration management
└── utils/            # Utilities and helpers
```

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** your changes: `git commit -m 'Add amazing feature'`
4. **Push** to the branch: `git push origin feature/amazing-feature`
5. **Open** a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google Gemini** for powerful dialogue generation
- **Deepgram** for high-quality voice synthesis
- **Rich** for beautiful terminal interfaces
- **Open Source Community** for inspiration and tools

---

**🎉 Ready for production use! Generate realistic meetings with voice synthesis today!**
