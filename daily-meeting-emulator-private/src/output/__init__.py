"""
Output module for Daily Meeting Emulator

Handles formatting and generation of meeting transcripts
in various formats with metadata and analysis.
"""

from .transcript import MeetingTranscript, TranscriptSegment, ParticipantInfo
from .formatters import TranscriptFormatter, TimestampFormatter, SimpleFormatter, DetailedFormatter

__all__ = [
    'MeetingTranscript',
    'TranscriptSegment',
    'ParticipantInfo',
    'TranscriptFormatter',
    'TimestampFormatter',
    'SimpleFormatter',
    'DetailedFormatter'
]