"""
Deepgram Voice Agent Integration

Integrates with Deepgram's Voice Agent API to provide:
- Text-to-Speech (TTS) for meeting participants
- Real-time voice generation
- Different voice personalities for different participants
- Audio file generation and playback
"""

import os
import asyncio
import logging
from typing import Dict, Optional, Any
from pathlib import Path
import aiofiles

try:
    from deepgram import DeepgramClient, SpeakOptions
    DEEPGRAM_AVAILABLE = True
except ImportError:
    DEEPGRAM_AVAILABLE = False
    DeepgramClient = None
    SpeakOptions = None

from rich.console import Console

logger = logging.getLogger(__name__)
console = Console()


class DeepgramVoiceAgent:
    """Deepgram Voice Agent for generating realistic speech"""
    
    # Voice mapping for different participant personalities
    VOICE_MAPPING = {
        "facilitator": "aura-asteria-en",    # Clear, authoritative
        "rambler": "aura-luna-en",           # Soft, detailed
        "questioner": "aura-stella-en",      # Curious, engaging
        "minimalist": "aura-zeus-en",        # Concise, direct
        "problem_solver": "aura-hera-en",    # Confident, solution-focused
        "storyteller": "aura-orion-en",      # Narrative, expressive
        "interrupter": "aura-arcas-en"       # Quick, energetic
    }
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Deepgram Voice Agent"""
        if not DEEPGRAM_AVAILABLE:
            raise ImportError("Deepgram SDK not available. Install with: pip install deepgram-sdk")
        
        self.api_key = api_key or os.getenv("DEEPGRAM_API_KEY")
        if not self.api_key:
            raise ValueError("Deepgram API key not found. Set DEEPGRAM_API_KEY environment variable.")
        
        self.client = DeepgramClient(self.api_key)
        self.audio_dir = Path("data/audio")
        self.audio_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("Deepgram Voice Agent initialized")
    
    def get_voice_for_personality(self, personality_type: str, participant_name: str = None) -> str:
        """Get appropriate voice for participant personality"""
        voice = self.VOICE_MAPPING.get(personality_type.lower(), "aura-asteria-en")
        
        # Add some variety based on participant name
        if participant_name:
            name_lower = participant_name.lower()
            if "sarah" in name_lower or "emily" in name_lower:
                voice = "aura-luna-en"  # Female voice
            elif "mike" in name_lower or "david" in name_lower:
                voice = "aura-zeus-en"  # Male voice
            elif "alex" in name_lower:
                voice = "aura-asteria-en"  # Neutral facilitator voice
        
        return voice
    
    async def text_to_speech(
        self, 
        text: str, 
        personality_type: str = "facilitator",
        participant_name: str = None,
        filename: str = None
    ) -> str:
        """Convert text to speech and save as audio file"""
        try:
            # Get appropriate voice
            voice = self.get_voice_for_personality(personality_type, participant_name)
            
            # Generate filename if not provided
            if not filename:
                safe_name = participant_name.replace(" ", "_") if participant_name else "participant"
                filename = f"{safe_name}_{hash(text) % 10000}.wav"
            
            filepath = self.audio_dir / filename
            
            # Configure speech options
            options = SpeakOptions(
                model=voice,
                encoding="linear16",
                sample_rate=24000
            )
            
            # Generate speech
            console.print(f"🎙️ [blue]Generating speech for {participant_name or 'participant'}...[/blue]")
            
            response = self.client.speak.v("1").save(
                filename=str(filepath),
                source={"text": text},
                options=options
            )
            
            if filepath.exists():
                console.print(f"✅ [green]Audio saved: {filepath}[/green]")
                return str(filepath)
            else:
                logger.error(f"Failed to generate audio file: {filepath}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating speech: {e}")
            console.print(f"❌ [red]Speech generation failed: {e}[/red]")
            return None
    
    async def generate_meeting_audio(
        self, 
        segments: list, 
        output_filename: str = "meeting_audio.wav"
    ) -> str:
        """Generate audio for entire meeting"""
        try:
            console.print(f"🎬 [yellow]Generating full meeting audio...[/yellow]")
            
            audio_files = []
            
            # Generate individual audio files for each segment
            for i, segment in enumerate(segments):
                participant_name = segment.get('participant', 'Unknown')
                content = segment.get('content', '')
                personality = segment.get('personality_type', 'facilitator')
                
                if content.strip():
                    filename = f"segment_{i:03d}_{participant_name}.wav"
                    audio_file = await self.text_to_speech(
                        text=content,
                        personality_type=personality,
                        participant_name=participant_name,
                        filename=filename
                    )
                    
                    if audio_file:
                        audio_files.append(audio_file)
            
            console.print(f"✅ [green]Generated {len(audio_files)} audio segments[/green]")
            
            # TODO: Concatenate audio files with appropriate timing
            # This would require additional audio processing libraries like pydub
            
            return str(self.audio_dir / output_filename)
            
        except Exception as e:
            logger.error(f"Error generating meeting audio: {e}")
            return None
    
    def is_available(self) -> bool:
        """Check if Deepgram Voice Agent is available"""
        return DEEPGRAM_AVAILABLE and bool(self.api_key)
    
    async def test_voice_generation(self) -> bool:
        """Test voice generation with a simple phrase"""
        try:
            test_text = "Hello, this is a test of the Deepgram voice generation system."
            result = await self.text_to_speech(
                text=test_text,
                personality_type="facilitator",
                participant_name="TestUser",
                filename="voice_test.wav"
            )
            return result is not None
        except Exception as e:
            logger.error(f"Voice generation test failed: {e}")
            return False


# Utility functions for voice integration
async def create_voice_agent(api_key: str = None) -> Optional[DeepgramVoiceAgent]:
    """Create and test Deepgram Voice Agent"""
    try:
        agent = DeepgramVoiceAgent(api_key)
        
        # Test the agent
        if await agent.test_voice_generation():
            console.print("🎙️ [green]Deepgram Voice Agent ready![/green]")
            return agent
        else:
            console.print("❌ [red]Voice Agent test failed[/red]")
            return None
            
    except Exception as e:
        console.print(f"❌ [red]Failed to initialize Voice Agent: {e}[/red]")
        return None


def get_available_voices() -> Dict[str, str]:
    """Get mapping of personality types to available voices"""
    return DeepgramVoiceAgent.VOICE_MAPPING.copy()
