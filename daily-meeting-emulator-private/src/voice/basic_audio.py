"""
Basic Audio Generator

Simple, reliable audio generation for meetings using only Deepgram.
No complex dependencies - just generates individual audio files.
"""

import os
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

try:
    from deepgram import DeepgramClient, SpeakOptions
    BASIC_AUDIO_AVAILABLE = True
except ImportError:
    BASIC_AUDIO_AVAILABLE = False
    DeepgramClient = None
    SpeakOptions = None

from rich.console import Console

logger = logging.getLogger(__name__)
console = Console()


class BasicAudioGenerator:
    """Basic audio generator - creates individual files for each segment"""
    
    # Voice mapping for different participant personalities
    VOICE_MAPPING = {
        "facilitator": "aura-asteria-en",    # Clear, authoritative
        "rambler": "aura-luna-en",           # Soft, detailed  
        "questioner": "aura-stella-en",      # Curious, engaging
        "minimalist": "aura-zeus-en",        # Concise, direct
        "problem_solver": "aura-hera-en",    # Confident, solution-focused
        "storyteller": "aura-orion-en",      # Narrative, expressive
        "interrupter": "aura-arcas-en"       # Quick, energetic
    }
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize basic audio generator"""
        if not BASIC_AUDIO_AVAILABLE:
            raise ImportError("Basic audio requires: pip install deepgram-sdk")
        
        self.api_key = api_key or os.getenv("DEEPGRAM_API_KEY")
        if not self.api_key:
            raise ValueError("Deepgram API key required for audio generation")
        
        self.client = DeepgramClient(self.api_key)
        
        # Output directory for audio files
        self.audio_dir = Path("data/audio")
        self.audio_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("Basic audio generator initialized")
        console.print("🎵 [green]Basic audio generator ready![/green]")
    
    def get_voice_for_participant(self, personality_type: str, participant_name: str = None) -> str:
        """Get appropriate voice for participant"""
        voice = self.VOICE_MAPPING.get(personality_type.lower(), "aura-asteria-en")
        
        # Add variety based on participant name
        if participant_name:
            name_lower = participant_name.lower()
            if "sarah" in name_lower or "emily" in name_lower:
                voice = "aura-luna-en"  # Female voice
            elif "mike" in name_lower or "david" in name_lower:
                voice = "aura-zeus-en"  # Male voice
            elif "alex" in name_lower:
                voice = "aura-asteria-en"  # Neutral facilitator voice
        
        return voice

    def _enhance_text_with_ssml(self, text: str, personality_type: str) -> str:
        """Enhance text with SSML for better speech quality based on personality"""

        # Clean text first
        clean_text = text.strip()

        # Apply personality-specific enhancements
        if personality_type.lower() == "facilitator":
            # Clear, authoritative tone with proper pauses
            enhanced = f'<speak><prosody rate="medium" pitch="medium">{clean_text}</prosody></speak>'

        elif personality_type.lower() == "rambler":
            # Slower, more detailed speech with longer pauses
            enhanced = f'<speak><prosody rate="slow" pitch="medium">{clean_text}<break time="1s"/></prosody></speak>'

        elif personality_type.lower() == "questioner":
            # Curious, engaging tone with emphasis on questions
            if "?" in clean_text:
                # Add emphasis to questions
                enhanced_text = clean_text.replace("?", '<emphasis level="moderate">?</emphasis>')
                enhanced = f'<speak><prosody rate="medium" pitch="high">{enhanced_text}</prosody></speak>'
            else:
                enhanced = f'<speak><prosody rate="medium" pitch="high">{clean_text}</prosody></speak>'

        elif personality_type.lower() == "minimalist":
            # Quick, concise speech
            enhanced = f'<speak><prosody rate="fast" pitch="medium">{clean_text}</prosody></speak>'

        elif personality_type.lower() == "problem_solver":
            # Confident, solution-focused tone
            enhanced = f'<speak><prosody rate="medium" pitch="low" volume="loud">{clean_text}</prosody></speak>'

        elif personality_type.lower() == "storyteller":
            # Narrative, expressive speech with varied pacing
            enhanced = f'<speak><prosody rate="medium" pitch="medium">{clean_text}<break time="0.5s"/></prosody></speak>'

        elif personality_type.lower() == "interrupter":
            # Quick, energetic speech
            enhanced = f'<speak><prosody rate="fast" pitch="high" volume="loud">{clean_text}</prosody></speak>'

        else:
            # Default enhancement
            enhanced = f'<speak><prosody rate="medium" pitch="medium">{clean_text}</prosody></speak>'

        # Add natural breathing patterns for longer texts
        if len(clean_text) > 100:
            # Add breathing pauses for long sentences
            enhanced = enhanced.replace(". ", '. <break time="0.3s"/>')
            enhanced = enhanced.replace(", ", ', <break time="0.1s"/>')

        # Enhance specific meeting content
        enhanced = self._enhance_meeting_content(enhanced)

        return enhanced

    def _enhance_meeting_content(self, ssml_text: str) -> str:
        """Add specific enhancements for meeting content"""

        # Emphasize important meeting keywords
        meeting_keywords = {
            "yesterday": '<emphasis level="moderate">yesterday</emphasis>',
            "today": '<emphasis level="moderate">today</emphasis>',
            "blocker": '<emphasis level="strong">blocker</emphasis>',
            "blocked": '<emphasis level="strong">blocked</emphasis>',
            "issue": '<emphasis level="moderate">issue</emphasis>',
            "problem": '<emphasis level="moderate">problem</emphasis>',
            "completed": '<emphasis level="moderate">completed</emphasis>',
            "finished": '<emphasis level="moderate">finished</emphasis>',
            "working on": '<emphasis level="moderate">working on</emphasis>',
            "need help": '<emphasis level="strong">need help</emphasis>',
            "urgent": '<emphasis level="strong">urgent</emphasis>',
            "deadline": '<emphasis level="strong">deadline</emphasis>'
        }

        enhanced = ssml_text
        for keyword, replacement in meeting_keywords.items():
            # Case-insensitive replacement within SSML
            import re
            pattern = re.compile(re.escape(keyword), re.IGNORECASE)
            enhanced = pattern.sub(replacement, enhanced)

        # Add pauses before important transitions
        transition_phrases = [
            "moving on to",
            "next item",
            "any questions",
            "any blockers",
            "that's all",
            "to summarize"
        ]

        for phrase in transition_phrases:
            enhanced = enhanced.replace(phrase, f'<break time="0.5s"/>{phrase}')

        return enhanced

    def generate_meeting_audio_files(
        self, 
        transcript_segments: List[Dict[str, Any]], 
        meeting_id: str = "meeting"
    ) -> List[str]:
        """
        Generate individual audio files for each segment
        
        Args:
            transcript_segments: List of segments with participant, content, personality_type
            meeting_id: Identifier for this meeting
            
        Returns:
            List of paths to generated audio files
        """
        try:
            console.print("🎬 [yellow]Generating meeting audio files...[/yellow]")
            
            audio_files = []
            
            for i, segment in enumerate(transcript_segments):
                participant_name = segment.get('participant', 'Unknown')
                content = segment.get('content', '')
                personality = segment.get('personality_type', 'facilitator')
                
                if content.strip():
                    console.print(f"🎙️ [blue]Generating audio {i+1}/{len(transcript_segments)}: {participant_name}[/blue]")
                    
                    # Generate audio for this segment
                    audio_file = self._generate_segment_audio(
                        text=content,
                        participant_name=participant_name,
                        personality_type=personality,
                        meeting_id=meeting_id,
                        segment_id=i
                    )
                    
                    if audio_file:
                        audio_files.append(audio_file)
            
            console.print(f"✅ [green]Generated {len(audio_files)} audio files[/green]")
            
            # Create a simple playlist file
            if audio_files:
                playlist_path = self._create_playlist(audio_files, meeting_id)
                console.print(f"📋 [green]Playlist created: {playlist_path}[/green]")
            
            return audio_files
                
        except Exception as e:
            logger.error(f"Error generating meeting audio: {e}")
            console.print(f"❌ [red]Meeting audio generation failed: {e}[/red]")
            return []
    
    def _generate_segment_audio(
        self,
        text: str,
        participant_name: str,
        personality_type: str,
        meeting_id: str,
        segment_id: int
    ) -> Optional[str]:
        """Generate audio for a single segment with enhanced formatting"""
        try:
            # Get appropriate voice
            voice = self.get_voice_for_participant(personality_type, participant_name)

            # Enhance text with SSML for better speech quality
            enhanced_text = self._enhance_text_with_ssml(text, personality_type)

            # Create filename
            safe_name = participant_name.replace(" ", "_")
            filename = f"{meeting_id}_segment_{segment_id:03d}_{safe_name}.wav"
            filepath = self.audio_dir / filename

            # Configure speech options with enhanced settings
            options = SpeakOptions(
                model=voice,
                encoding="linear16",
                sample_rate=24000,
                container="wav"
            )

            # Generate speech with enhanced text
            response = self.client.speak.v("1").save(
                filename=str(filepath),
                source={"text": enhanced_text},
                options=options
            )
            
            if filepath.exists():
                console.print(f"   ✅ [green]Created: {filename}[/green]")
                return str(filepath)
            else:
                logger.error(f"Failed to generate audio: {filepath}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating segment audio: {e}")
            console.print(f"   ❌ [red]Failed: {e}[/red]")
            return None
    
    def _create_playlist(self, audio_files: List[str], meeting_id: str) -> str:
        """Create a simple playlist file"""
        try:
            playlist_path = self.audio_dir / f"{meeting_id}_playlist.txt"
            
            with open(playlist_path, 'w', encoding='utf-8') as f:
                f.write(f"# Meeting Audio Playlist: {meeting_id}\n")
                f.write(f"# Generated audio files in order:\n\n")
                
                for i, file_path in enumerate(audio_files):
                    filename = Path(file_path).name
                    f.write(f"{i+1:2d}. {filename}\n")
                
                f.write(f"\n# Total files: {len(audio_files)}\n")
                f.write(f"# To play all files in order, use your audio player's playlist feature\n")
            
            return str(playlist_path)
            
        except Exception as e:
            logger.error(f"Error creating playlist: {e}")
            return None
    
    def is_available(self) -> bool:
        """Check if basic audio generation is available"""
        return BASIC_AUDIO_AVAILABLE and bool(self.api_key)


# Utility functions
def create_basic_audio_generator(api_key: str = None) -> Optional[BasicAudioGenerator]:
    """Create and initialize basic audio generator"""
    try:
        generator = BasicAudioGenerator(api_key)
        return generator
    except Exception as e:
        console.print(f"❌ [red]Failed to initialize basic audio generator: {e}[/red]")
        return None


def generate_meeting_audio_basic(transcript, meeting_id: str = None) -> Optional[List[str]]:
    """
    Generate basic meeting audio from transcript
    
    Args:
        transcript: MeetingTranscript object
        meeting_id: Optional meeting identifier
        
    Returns:
        List of paths to generated audio files
    """
    try:
        generator = create_basic_audio_generator()
        if not generator:
            return None
        
        # Convert transcript to segments format
        segments = []
        for segment in transcript.segments:
            # Find participant info
            participant_info = None
            for p in transcript.participants:
                if p.name == segment.participant:
                    participant_info = p
                    break
            
            segments.append({
                'participant': segment.participant,
                'content': segment.content,
                'personality_type': participant_info.personality if participant_info else 'facilitator'
            })
        
        # Generate meeting ID if not provided
        if not meeting_id:
            meeting_id = transcript.metadata.get('timestamp', 'unknown')
        
        # Generate audio files
        audio_files = generator.generate_meeting_audio_files(segments, meeting_id)
        
        return audio_files
        
    except Exception as e:
        logger.error(f"Error generating basic meeting audio: {e}")
        return None
