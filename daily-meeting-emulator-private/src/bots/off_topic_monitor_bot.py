"""
Off-Topic Monitor Bot

AI-powered bot that monitors daily standup meetings for off-topic conversations
using Gemini 2.5 Flash and gently interrupts to suggest establishing separate meetings.
"""

import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Import from parent modules
import sys
from pathlib import Path
src_path = Path(__file__).parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from bots.base_bot import BaseBot, BotConfig
from output.transcript import TranscriptSegment
from dialogue.gemini_engine import GeminiEngine, GenerationConfig

logger = logging.getLogger(__name__)


class OffTopicCategory(Enum):
    """Categories of off-topic conversations"""
    TECHNICAL_DEEP_DIVE = "technical_deep_dive"
    PROBLEM_SOLVING = "problem_solving"
    ARCHITECTURE_DISCUSSION = "architecture_discussion"
    DEBUGGING_STORIES = "debugging_stories"
    PROCESS_DISCUSSION = "process_discussion"
    PERSONAL_CONVERSATION = "personal_conversation"
    LENGTHY_EXPLANATION = "lengthy_explanation"
    ON_TOPIC = "on_topic"


@dataclass
class OffTopicAnalysis:
    """Result of off-topic content analysis"""
    is_off_topic: bool
    confidence: float
    category: OffTopicCategory
    reasoning: str
    suggested_action: str
    urgency: str  # "low", "medium", "high"


@dataclass
class InterventionDecision:
    """Decision about whether and how to intervene"""
    should_interrupt: bool
    intervention_text: str
    timing: str  # "immediate", "after_speaker", "end_of_phase"
    confidence: float


class OffTopicMonitorBot(BaseBot):
    """
    AI-powered bot that monitors conversations for off-topic discussions

    Uses Gemini 2.5 Flash to analyze conversation content and determine
    when discussions have moved away from standup-appropriate topics.
    """
    
    def __init__(self, config: Dict[str, Any], api_key: str):
        """
        Initialize the off-topic monitor bot
        
        Args:
            config: Bot configuration dictionary
            api_key: Gemini API key for content analysis
        """
        super().__init__(name="OffTopicMonitor", config=config, api_key=api_key)
        
        # Bot-specific configuration
        self.sensitivity = config.get('sensitivity', 0.7)
        self.categories_to_monitor = config.get('categories', [
            'technical_deep_dive',
            'problem_solving', 
            'architecture_discussion',
            'debugging_stories',
            'process_discussion'
        ])
        
        # Analysis cache to avoid re-analyzing similar content
        self.analysis_cache: Dict[str, OffTopicAnalysis] = {}
        
        logger.info(f"OffTopicMonitorBot initialized with sensitivity {self.sensitivity}")
    
    def _initialize_bot(self):
        """Initialize Gemini engine for content analysis"""
        if not self.api_key:
            raise ValueError("Gemini API key required for OffTopicMonitorBot")

        # Use Gemini 2.5 Flash for fast, efficient analysis
        generation_config = GenerationConfig(
            temperature=0.3,  # Low temperature for consistent analysis
            max_output_tokens=200,  # Short responses for analysis
            top_p=0.8,
            top_k=20
        )

        self.gemini_engine = GeminiEngine(
            api_key=self.api_key,
            config=generation_config,
            model_name="gemini-2.5-flash-preview-05-20"  # Use Gemini 2.5 Flash latest available
        )

        logger.info("Gemini 2.5 Flash engine initialized for off-topic analysis")
    
    def _should_intervene(self, segment: TranscriptSegment) -> bool:
        """
        Analyze conversation segment and decide whether to intervene
        
        Args:
            segment: Conversation segment to analyze
            
        Returns:
            True if bot should intervene, False otherwise
        """
        # Check intervention limits
        if not self._check_intervention_limits():
            return False
        
        # Don't interrupt too frequently
        if (segment.timestamp - self.last_intervention_time) < 30.0:  # 30 seconds minimum
            return False
        
        # Skip very short messages
        if len(segment.content.split()) < 10:
            return False
        
        # Analyze content for off-topic discussion
        analysis = self._analyze_content_with_ai(segment)
        
        # Decide based on analysis
        return (analysis.is_off_topic and 
                analysis.confidence >= self.sensitivity and
                analysis.category.value in self.categories_to_monitor)
    
    def _generate_intervention(self, segment: TranscriptSegment) -> str:
        """
        Generate appropriate intervention text
        
        Args:
            segment: Conversation segment that triggered intervention
            
        Returns:
            Intervention text to speak
        """
        # Analyze the content to get specific suggestions
        analysis = self._analyze_content_with_ai(segment)
        
        # Generate contextual intervention
        base_phrase = self._get_interruption_phrase()
        specific_suggestion = self._get_specific_suggestion(analysis)
        
        # Combine into natural intervention
        intervention = f"{base_phrase} {specific_suggestion}"
        
        return intervention
    
    def _analyze_content_with_ai(self, segment: TranscriptSegment) -> OffTopicAnalysis:
        """
        Use Gemini 2.5 Flash to analyze content for off-topic discussions

        Args:
            segment: Conversation segment to analyze

        Returns:
            Analysis result with off-topic classification
        """
        # Check cache first
        cache_key = f"{segment.participant}:{hash(segment.content)}"
        if cache_key in self.analysis_cache:
            return self.analysis_cache[cache_key]
        
        # Build analysis prompt
        context = self._get_recent_context(3)
        prompt = self._build_analysis_prompt(segment.content, context, segment.participant)
        
        try:
            # Get AI analysis
            response = self.gemini_engine._generate_with_retry(prompt)
            analysis = self._parse_analysis_response(response)
            
            # Cache result
            self.analysis_cache[cache_key] = analysis
            
            return analysis
            
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            # Fallback to rule-based analysis
            return self._fallback_analysis(segment.content)
    
    def _build_analysis_prompt(self, content: str, context: str, speaker: str) -> str:
        """Build prompt for Gemini analysis"""
        return f"""You are an AI assistant analyzing a daily standup meeting conversation to detect off-topic discussions.

STANDUP MEETING CONTEXT:
Daily standups should focus on:
- What was accomplished yesterday
- What will be worked on today  
- Any blockers or impediments
- Brief status updates
- Quick clarifying questions

OFF-TOPIC INDICATORS:
- Technical deep-dives or implementation details
- Problem-solving or debugging sessions
- Architecture or design discussions
- Lengthy explanations or stories
- Process or methodology discussions
- Personal conversations

RECENT CONVERSATION CONTEXT:
{context}

CURRENT SPEAKER: {speaker}
CURRENT STATEMENT: "{content}"

ANALYSIS TASK:
Analyze if the current statement is off-topic for a standup meeting.

Respond in this exact format:
OFF_TOPIC: [true/false]
CONFIDENCE: [0.0-1.0]
CATEGORY: [technical_deep_dive|problem_solving|architecture_discussion|debugging_stories|process_discussion|personal_conversation|on_topic]
REASONING: [brief explanation]
SUGGESTED_ACTION: [specific suggestion for handling this topic]
URGENCY: [low|medium|high]

Example response:
OFF_TOPIC: true
CONFIDENCE: 0.8
CATEGORY: technical_deep_dive
REASONING: Speaker is explaining detailed implementation of authentication system
SUGGESTED_ACTION: Schedule a technical review session for authentication implementation
URGENCY: medium"""
    
    def _parse_analysis_response(self, response: str) -> OffTopicAnalysis:
        """Parse Gemini response into structured analysis"""
        try:
            lines = response.strip().split('\n')
            result = {}
            
            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    result[key.strip().lower()] = value.strip()
            
            return OffTopicAnalysis(
                is_off_topic=result.get('off_topic', 'false').lower() == 'true',
                confidence=float(result.get('confidence', '0.0')),
                category=OffTopicCategory(result.get('category', 'on_topic')),
                reasoning=result.get('reasoning', 'No reasoning provided'),
                suggested_action=result.get('suggested_action', 'Continue discussion in separate meeting'),
                urgency=result.get('urgency', 'medium')
            )
            
        except Exception as e:
            logger.error(f"Failed to parse analysis response: {e}")
            return self._fallback_analysis(response)
    
    def _fallback_analysis(self, content: str) -> OffTopicAnalysis:
        """Fallback rule-based analysis when AI fails"""
        content_lower = content.lower()
        
        # Simple keyword-based detection
        technical_keywords = ['implementation', 'algorithm', 'database', 'architecture', 'design pattern']
        problem_keywords = ['debug', 'error', 'exception', 'fix', 'troubleshoot']
        
        if any(keyword in content_lower for keyword in technical_keywords):
            return OffTopicAnalysis(
                is_off_topic=True,
                confidence=0.6,
                category=OffTopicCategory.TECHNICAL_DEEP_DIVE,
                reasoning="Contains technical implementation details",
                suggested_action="Schedule technical discussion",
                urgency="medium"
            )
        elif any(keyword in content_lower for keyword in problem_keywords):
            return OffTopicAnalysis(
                is_off_topic=True,
                confidence=0.6,
                category=OffTopicCategory.PROBLEM_SOLVING,
                reasoning="Contains problem-solving discussion",
                suggested_action="Schedule debugging session",
                urgency="medium"
            )
        else:
            return OffTopicAnalysis(
                is_off_topic=False,
                confidence=0.5,
                category=OffTopicCategory.ON_TOPIC,
                reasoning="Appears to be standup-appropriate content",
                suggested_action="Continue with standup",
                urgency="low"
            )
    
    def _get_specific_suggestion(self, analysis: OffTopicAnalysis) -> str:
        """Get specific suggestion based on analysis"""
        suggestions = {
            OffTopicCategory.TECHNICAL_DEEP_DIVE: "Let's schedule a technical deep-dive session to explore this properly.",
            OffTopicCategory.PROBLEM_SOLVING: "This sounds like it needs focused problem-solving time - shall we set up a debugging session?",
            OffTopicCategory.ARCHITECTURE_DISCUSSION: "This architecture discussion deserves dedicated time - let's schedule a design review.",
            OffTopicCategory.DEBUGGING_STORIES: "Let's capture this debugging experience for a separate technical discussion.",
            OffTopicCategory.PROCESS_DISCUSSION: "This process topic would be great for our next retrospective or process improvement meeting.",
            OffTopicCategory.PERSONAL_CONVERSATION: "Let's continue this conversation after the standup."
        }
        
        return suggestions.get(analysis.category, analysis.suggested_action)

    def get_analysis_stats(self) -> Dict[str, Any]:
        """Get statistics about bot's analysis performance"""
        total_segments = len(self.conversation_history)
        if total_segments == 0:
            return {"total_segments": 0, "interventions": 0, "intervention_rate": 0.0}

        return {
            "total_segments": total_segments,
            "interventions": self.interventions_count,
            "intervention_rate": self.interventions_count / total_segments,
            "cache_size": len(self.analysis_cache),
            "sensitivity": self.sensitivity,
            "categories_monitored": self.categories_to_monitor
        }

    def update_sensitivity(self, new_sensitivity: float):
        """Update bot sensitivity during meeting"""
        if 0.0 <= new_sensitivity <= 1.0:
            self.sensitivity = new_sensitivity
            logger.info(f"Bot sensitivity updated to {new_sensitivity}")
        else:
            logger.warning(f"Invalid sensitivity value: {new_sensitivity}")

    def clear_analysis_cache(self):
        """Clear analysis cache (useful for testing)"""
        self.analysis_cache.clear()
        logger.info("Analysis cache cleared")


# Utility functions for bot creation and management

def create_off_topic_monitor_bot(config: Dict[str, Any], api_key: str) -> OffTopicMonitorBot:
    """
    Factory function to create and configure an OffTopicMonitorBot

    Args:
        config: Bot configuration dictionary
        api_key: Gemini API key

    Returns:
        Configured OffTopicMonitorBot instance
    """
    try:
        bot = OffTopicMonitorBot(config, api_key)
        logger.info("OffTopicMonitorBot created successfully")
        return bot
    except Exception as e:
        logger.error(f"Failed to create OffTopicMonitorBot: {e}")
        raise


def get_default_bot_config() -> Dict[str, Any]:
    """Get default configuration for OffTopicMonitorBot"""
    return {
        'bot_config': {
            'enabled': True,
            'sensitivity': 0.7,
            'max_interventions_per_meeting': 3,
            'interruption_style': 'gentle',
            'respect_hierarchy': True,
            'intervention_delay': 1.0
        },
        'sensitivity': 0.7,
        'categories': [
            'technical_deep_dive',
            'problem_solving',
            'architecture_discussion',
            'debugging_stories',
            'process_discussion'
        ]
    }
