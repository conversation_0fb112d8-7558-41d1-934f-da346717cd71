"""
Base Bot Class

Abstract base class for all meeting bots that can participate in meetings
and provide various AI-powered functionalities.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

# Import from parent modules
import sys
from pathlib import Path
src_path = Path(__file__).parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from participants.base_participant import BaseParticipant, PersonalityTraits, Role, PersonalityType
from output.transcript import TranscriptSegment, ConversationContext

logger = logging.getLogger(__name__)


@dataclass
class BotConfig:
    """Configuration for meeting bots"""
    enabled: bool = True
    sensitivity: float = 0.7
    max_interventions_per_meeting: int = 3
    interruption_style: str = "gentle"  # gentle, direct, formal
    respect_hierarchy: bool = True
    intervention_delay: float = 1.0  # seconds to wait before intervening


class BaseBot(BaseParticipant, ABC):
    """
    Abstract base class for all meeting bots
    
    Bo<PERSON> are special participants that can:
    - Monitor conversations in real-time
    - Analyze content using AI
    - Intervene when appropriate
    - Provide meeting facilitation
    """
    
    def __init__(self, 
                 name: str, 
                 config: Dict[str, Any],
                 api_key: str = None):
        """
        Initialize the bot
        
        Args:
            name: Bot name (e.g., "OffTopicMonitor")
            config: Bot configuration dictionary
            api_key: API key for AI services
        """
        # Initialize as a special participant
        super().__init__(
            name=name,
            role=Role.FACILITATOR,  # Bots typically have facilitator-like authority
            personality_type=PersonalityType.FACILITATOR,
            personality_traits=PersonalityTraits(
                verbosity=0.3,  # Bots should be concise
                technical_detail=0.2,  # Avoid technical details
                interruption_tendency=0.8,  # High tendency to interrupt when needed
                curiosity=0.1,  # Low curiosity, focused on their task
                off_topic_probability=0.0,  # Bots stay on topic
                time_awareness=0.9  # High time awareness
            )
        )
        
        self.config = BotConfig(**config.get('bot_config', {}))
        self.api_key = api_key
        
        # Bot state
        self.conversation_history: List[TranscriptSegment] = []
        self.interventions_count = 0
        self.last_intervention_time = 0.0
        self.is_active = self.config.enabled
        
        # Bot-specific initialization
        self._initialize_bot()
        
        logger.info(f"Bot {name} initialized with config: {self.config}")
    
    @abstractmethod
    def _initialize_bot(self):
        """Initialize bot-specific components (implemented by subclasses)"""
        pass
    
    def process_conversation_segment(self, segment: TranscriptSegment) -> Optional[str]:
        """
        Process a new conversation segment and decide whether to intervene
        
        Args:
            segment: New conversation segment to analyze
            
        Returns:
            Intervention text if bot decides to interrupt, None otherwise
        """
        if not self.is_active:
            return None
        
        # Add to conversation history
        self.conversation_history.append(segment)
        
        # Check if we should intervene
        if self._should_intervene(segment):
            intervention = self._generate_intervention(segment)
            if intervention:
                self.interventions_count += 1
                self.last_intervention_time = segment.timestamp
                logger.info(f"Bot {self.name} intervening: {intervention[:50]}...")
                return intervention
        
        return None
    
    @abstractmethod
    def _should_intervene(self, segment: TranscriptSegment) -> bool:
        """
        Decide whether to intervene based on the conversation segment
        
        Args:
            segment: Conversation segment to analyze
            
        Returns:
            True if bot should intervene, False otherwise
        """
        pass
    
    @abstractmethod
    def _generate_intervention(self, segment: TranscriptSegment) -> str:
        """
        Generate intervention text for the given segment
        
        Args:
            segment: Conversation segment that triggered intervention
            
        Returns:
            Intervention text to speak
        """
        pass
    
    def _check_intervention_limits(self) -> bool:
        """Check if bot has reached intervention limits"""
        return self.interventions_count < self.config.max_interventions_per_meeting
    
    def _get_recent_context(self, num_segments: int = 5) -> str:
        """Get recent conversation context as text"""
        if not self.conversation_history:
            return ""
        
        recent_segments = self.conversation_history[-num_segments:]
        context_lines = []
        
        for segment in recent_segments:
            context_lines.append(f"{segment.participant}: {segment.content}")
        
        return "\n".join(context_lines)
    
    def _get_interruption_phrase(self, style: str = None) -> str:
        """Get appropriate interruption phrase based on style"""
        style = style or self.config.interruption_style
        
        phrases = {
            "gentle": [
                "Excuse me, I think this might be a great topic for a separate discussion...",
                "This sounds really important - perhaps we could schedule a focused session for this?",
                "I'd love to hear more about this in a dedicated meeting where we can give it proper attention...",
                "This is valuable discussion - should we capture this for a follow-up meeting?"
            ],
            "direct": [
                "Let's table this for a separate meeting to keep our standup on track.",
                "This seems like a topic for a focused discussion outside of standup.",
                "We should schedule time specifically for this conversation.",
                "Let's capture this as an action item for a separate meeting."
            ],
            "formal": [
                "I suggest we schedule a separate meeting to properly address this topic.",
                "This discussion would benefit from dedicated time outside of our standup.",
                "I recommend we create a focused session to explore this further.",
                "This warrants a separate discussion to ensure adequate time and attention."
            ]
        }
        
        import random
        return random.choice(phrases.get(style, phrases["gentle"]))
    
    def reset_for_new_meeting(self):
        """Reset bot state for a new meeting"""
        self.conversation_history.clear()
        self.interventions_count = 0
        self.last_intervention_time = 0.0
        logger.info(f"Bot {self.name} reset for new meeting")
    
    def get_bot_stats(self) -> Dict[str, Any]:
        """Get bot performance statistics"""
        return {
            "name": self.name,
            "interventions_count": self.interventions_count,
            "segments_processed": len(self.conversation_history),
            "is_active": self.is_active,
            "config": self.config.__dict__
        }
