"""
Speech Timing Utilities

Calculates realistic speech duration based on text content,
considering speaking speed, pauses, and natural speech patterns.
"""

import re
import logging
from typing import Dict, List, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class SpeechMetrics:
    """Metrics for speech timing calculation"""
    words_per_minute: float = 150.0  # Average speaking speed
    characters_per_minute: float = 900.0  # Average character speed
    pause_after_sentence: float = 0.5  # Seconds pause after sentence
    pause_after_comma: float = 0.2  # Seconds pause after comma
    pause_after_question: float = 0.7  # Seconds pause after question
    thinking_pause: float = 0.3  # Pause for "um", "uh", etc.


class SpeechTimingCalculator:
    """
    Calculates realistic speech timing based on text content
    """
    
    def __init__(self, metrics: SpeechMetrics = None):
        """Initialize with speech metrics"""
        self.metrics = metrics or SpeechMetrics()
        
        # Personality-based speaking speed adjustments
        self.personality_speed_multipliers = {
            'facilitator': 1.0,      # Normal speed
            'rambler': 0.85,         # Slower, more detailed
            'questioner': 1.1,       # Slightly faster, curious
            'minimalist': 1.2,       # Faster, concise
            'problem_solver': 0.95,  # Thoughtful pace
            'storyteller': 0.9,      # Slower, narrative style
            'interrupter': 1.3       # Fast, eager to speak
        }
        
        # Words that add thinking pauses
        self.thinking_words = {
            'um', 'uh', 'er', 'well', 'so', 'actually', 'basically',
            'you know', 'i mean', 'like', 'kind of', 'sort of'
        }
        
        # Technical terms that slow down speech
        self.technical_terms = {
            'api', 'database', 'authentication', 'microservice', 'kubernetes',
            'docker', 'deployment', 'architecture', 'infrastructure', 'algorithm',
            'framework', 'library', 'repository', 'configuration', 'integration'
        }
    
    def calculate_speech_duration(self, 
                                text: str, 
                                personality_type: str = 'facilitator',
                                role: str = 'developer') -> float:
        """
        Calculate realistic speech duration for given text
        
        Args:
            text: The text to be spoken
            personality_type: Speaker's personality type
            role: Speaker's role (affects technical term handling)
            
        Returns:
            Duration in seconds
        """
        if not text or not text.strip():
            return 0.0
        
        # Clean and normalize text
        clean_text = self._clean_text(text)
        
        # Base timing calculation
        base_duration = self._calculate_base_duration(clean_text)
        
        # Add pauses for punctuation
        pause_duration = self._calculate_pause_duration(clean_text)
        
        # Add thinking pauses
        thinking_duration = self._calculate_thinking_pauses(clean_text)
        
        # Add technical term slowdown
        technical_duration = self._calculate_technical_slowdown(clean_text, role)
        
        # Apply personality speed multiplier
        personality_multiplier = self.personality_speed_multipliers.get(
            personality_type, 1.0
        )
        
        total_duration = (base_duration + pause_duration + thinking_duration + technical_duration) / personality_multiplier
        
        # Minimum duration (even short phrases take some time)
        min_duration = max(0.5, len(clean_text.split()) * 0.2)
        
        return max(min_duration, total_duration)
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text for timing calculation"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove markdown formatting
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*([^*]+)\*', r'\1', text)      # Italic
        text = re.sub(r'`([^`]+)`', r'\1', text)        # Code
        
        return text
    
    def _calculate_base_duration(self, text: str) -> float:
        """Calculate base speaking duration"""
        words = text.split()
        word_count = len(words)
        
        # Use words per minute as primary metric
        base_duration = (word_count / self.metrics.words_per_minute) * 60
        
        # Adjust for character count (longer words take more time)
        char_count = len(text)
        char_duration = (char_count / self.metrics.characters_per_minute) * 60
        
        # Use average of both methods
        return (base_duration + char_duration) / 2
    
    def _calculate_pause_duration(self, text: str) -> float:
        """Calculate duration for punctuation pauses"""
        pause_duration = 0.0
        
        # Count different types of punctuation
        sentences = len(re.findall(r'[.!]', text))
        questions = len(re.findall(r'\?', text))
        commas = len(re.findall(r',', text))
        
        # Add pauses
        pause_duration += sentences * self.metrics.pause_after_sentence
        pause_duration += questions * self.metrics.pause_after_question
        pause_duration += commas * self.metrics.pause_after_comma
        
        return pause_duration
    
    def _calculate_thinking_pauses(self, text: str) -> float:
        """Calculate duration for thinking words and hesitations"""
        thinking_duration = 0.0
        text_lower = text.lower()
        
        for thinking_word in self.thinking_words:
            count = text_lower.count(thinking_word)
            thinking_duration += count * self.metrics.thinking_pause
        
        return thinking_duration
    
    def _calculate_technical_slowdown(self, text: str, role: str) -> float:
        """Calculate slowdown for technical terms"""
        if role not in ['developer', 'senior_developer', 'devops_engineer', 'qa_engineer']:
            return 0.0  # Non-technical roles don't slow down for tech terms
        
        technical_duration = 0.0
        text_lower = text.lower()
        
        for tech_term in self.technical_terms:
            count = text_lower.count(tech_term)
            # Technical terms add slight pause for emphasis
            technical_duration += count * 0.1
        
        return technical_duration
    
    def estimate_speaking_speed(self, text: str, duration: float) -> Dict[str, float]:
        """
        Estimate speaking speed metrics from text and duration
        
        Args:
            text: The spoken text
            duration: Actual duration in seconds
            
        Returns:
            Dictionary with speed metrics
        """
        if duration <= 0:
            return {'wpm': 0, 'cpm': 0}
        
        words = len(text.split())
        characters = len(text)
        
        wpm = (words / duration) * 60
        cpm = (characters / duration) * 60
        
        return {
            'words_per_minute': wpm,
            'characters_per_minute': cpm,
            'words': words,
            'characters': characters,
            'duration': duration
        }
    
    def format_duration(self, duration: float) -> str:
        """Format duration for display"""
        if duration < 1:
            return f"{duration:.1f}s"
        elif duration < 60:
            return f"{duration:.0f}s"
        else:
            minutes = int(duration // 60)
            seconds = int(duration % 60)
            return f"{minutes}m{seconds:02d}s"


# Global instance for easy access
speech_calculator = SpeechTimingCalculator()


def calculate_speech_duration(text: str, 
                            personality_type: str = 'facilitator',
                            role: str = 'developer') -> float:
    """
    Convenience function to calculate speech duration
    
    Args:
        text: Text to be spoken
        personality_type: Speaker's personality
        role: Speaker's role
        
    Returns:
        Duration in seconds
    """
    return speech_calculator.calculate_speech_duration(text, personality_type, role)


def format_speech_duration(duration: float) -> str:
    """
    Convenience function to format duration
    
    Args:
        duration: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    return speech_calculator.format_duration(duration)


if __name__ == "__main__":
    # Test the speech timing calculator
    calculator = SpeechTimingCalculator()
    
    test_phrases = [
        ("Good morning everyone!", "facilitator", "scrum_master"),
        ("Yesterday I worked on the authentication API and fixed several bugs in the user registration flow.", "rambler", "senior_developer"),
        ("Quick question - when will the database migration be ready?", "questioner", "junior_developer"),
        ("Done.", "minimalist", "developer"),
        ("Well, um, I think we need to consider the microservice architecture implications here.", "problem_solver", "senior_developer")
    ]
    
    print("🎤 SPEECH TIMING CALCULATOR TEST")
    print("=" * 50)
    
    for text, personality, role in test_phrases:
        duration = calculator.calculate_speech_duration(text, personality, role)
        speed_metrics = calculator.estimate_speaking_speed(text, duration)
        
        print(f"\n📝 Text: \"{text}\"")
        print(f"👤 {personality} ({role})")
        print(f"⏱️  Duration: {calculator.format_duration(duration)}")
        print(f"🗣️  Speed: {speed_metrics['words_per_minute']:.0f} WPM")
        print(f"📊 Words: {speed_metrics['words']}, Characters: {speed_metrics['characters']}")
