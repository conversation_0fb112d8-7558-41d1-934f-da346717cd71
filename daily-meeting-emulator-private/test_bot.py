#!/usr/bin/env python3
"""
Test script for Off-Topic Monitor Bot

This script tests the bot functionality independently to ensure
it works correctly before integration with the full meeting simulator.
"""

import os
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from dotenv import load_dotenv
from rich.console import Console

# Load environment variables
load_dotenv()

console = Console()


def test_bot_creation():
    """Test creating the off-topic monitor bot"""
    console.print("🧪 [blue]Testing Bot Creation[/blue]")
    
    try:
        from bots.off_topic_monitor_bot import create_off_topic_monitor_bot, get_default_bot_config
        
        # Check API key
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            console.print("❌ [red]GEMINI_API_KEY not found in environment[/red]")
            return False
        
        # Create bot with default config
        config = get_default_bot_config()
        bot = create_off_topic_monitor_bot(config, api_key)
        
        console.print(f"✅ [green]Bot created successfully: {bot.name}[/green]")
        console.print(f"   • Sensitivity: {bot.sensitivity}")
        console.print(f"   • Categories monitored: {len(bot.categories_to_monitor)}")
        console.print(f"   • Max interventions: {bot.config.max_interventions_per_meeting}")
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]Bot creation failed: {e}[/red]")
        return False


def test_bot_analysis():
    """Test bot content analysis"""
    console.print("\n🧪 [blue]Testing Bot Analysis[/blue]")
    
    try:
        from bots.off_topic_monitor_bot import create_off_topic_monitor_bot, get_default_bot_config
        from output.transcript import TranscriptSegment
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            console.print("❌ [red]GEMINI_API_KEY not found[/red]")
            return False
        
        # Create bot
        config = get_default_bot_config()
        bot = create_off_topic_monitor_bot(config, api_key)
        
        # Test cases
        test_cases = [
            {
                "content": "Yesterday I completed the user authentication feature. Today I'm working on the dashboard API.",
                "expected_off_topic": False,
                "description": "Normal standup update"
            },
            {
                "content": "So I was debugging this really complex authentication issue and it turns out the problem was in the JWT token validation logic where we were using the wrong algorithm for signature verification...",
                "expected_off_topic": True,
                "description": "Technical deep-dive"
            },
            {
                "content": "I think we should refactor our entire microservices architecture to use event sourcing patterns with CQRS implementation.",
                "expected_off_topic": True,
                "description": "Architecture discussion"
            },
            {
                "content": "I'm blocked on the payment integration because the third-party API is down.",
                "expected_off_topic": False,
                "description": "Blocker statement"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            console.print(f"\n📝 [yellow]Test Case {i}: {test_case['description']}[/yellow]")
            console.print(f"   Content: \"{test_case['content'][:60]}...\"")
            
            # Create test segment
            segment = TranscriptSegment(
                timestamp=0.0,
                participant="TestUser",
                content=test_case['content'],
                is_off_topic=False,
                interruption=False,
                topics=[]
            )
            
            # Test bot analysis
            intervention = bot.process_conversation_segment(segment)
            
            if intervention and test_case['expected_off_topic']:
                console.print(f"   ✅ [green]Correctly detected off-topic content[/green]")
                console.print(f"   🤖 Bot intervention: \"{intervention[:60]}...\"")
            elif not intervention and not test_case['expected_off_topic']:
                console.print(f"   ✅ [green]Correctly identified on-topic content[/green]")
            elif intervention and not test_case['expected_off_topic']:
                console.print(f"   ⚠️ [yellow]False positive: Bot intervened on on-topic content[/yellow]")
                console.print(f"   🤖 Bot intervention: \"{intervention[:60]}...\"")
            else:
                console.print(f"   ⚠️ [yellow]False negative: Bot missed off-topic content[/yellow]")
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]Bot analysis test failed: {e}[/red]")
        import traceback
        console.print(traceback.format_exc())
        return False


def test_bot_integration():
    """Test bot integration with meeting simulator"""
    console.print("\n🧪 [blue]Testing Bot Integration[/blue]")
    
    try:
        from meeting.meeting_simulator import MeetingSimulator
        from config.config_loader import ConfigLoader
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            console.print("❌ [red]GEMINI_API_KEY not found[/red]")
            return False
        
        # Load configuration
        config_loader = ConfigLoader()
        meeting_config = config_loader.load_config('config/default_meeting.yaml')
        
        # Override for quick test
        meeting_config['meeting']['duration_minutes'] = 2
        meeting_config['meeting']['participant_count'] = 2
        
        # Create simulator with bots enabled
        simulator = MeetingSimulator(
            meeting_config, 
            api_key, 
            real_time=False,  # Fast mode for testing
            enable_voice=False,
            enable_bots=True
        )
        
        console.print(f"✅ [green]Meeting simulator created with {len(simulator.bots)} bots[/green]")
        
        # Test a short meeting
        console.print("🎭 [yellow]Running test meeting...[/yellow]")
        transcript = simulator.simulate_meeting()
        
        console.print(f"✅ [green]Test meeting completed[/green]")
        console.print(f"   • Duration: {transcript.duration:.1f} minutes")
        console.print(f"   • Segments: {len(transcript.segments)}")
        console.print(f"   • Participants: {len(transcript.participants)}")
        
        # Check for bot interventions
        bot_segments = [s for s in transcript.segments if "Monitor" in s.participant]
        console.print(f"   • Bot interventions: {len(bot_segments)}")
        
        if bot_segments:
            console.print("   🤖 [blue]Bot interventions found:[/blue]")
            for segment in bot_segments:
                console.print(f"      - {segment.content[:60]}...")
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]Integration test failed: {e}[/red]")
        import traceback
        console.print(traceback.format_exc())
        return False


def main():
    """Run all bot tests"""
    console.print("🤖 [bold blue]Off-Topic Monitor Bot Test Suite[/bold blue]")
    console.print("=" * 60)
    
    tests = [
        ("Bot Creation", test_bot_creation),
        ("Bot Analysis", test_bot_analysis),
        ("Bot Integration", test_bot_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        console.print(f"\n🔍 [bold]Running {test_name} Test[/bold]")
        console.print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            console.print(f"❌ [red]Test {test_name} crashed: {e}[/red]")
            results.append((test_name, False))
    
    # Summary
    console.print("\n" + "=" * 60)
    console.print("📊 [bold blue]Test Results Summary[/bold blue]")
    console.print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        console.print(f"   {test_name}: {status}")
    
    console.print(f"\n🎯 [bold]Overall: {passed}/{total} tests passed[/bold]")
    
    if passed == total:
        console.print("🎉 [green]All tests passed! Bot is ready for use.[/green]")
    else:
        console.print("⚠️ [yellow]Some tests failed. Check the output above for details.[/yellow]")


if __name__ == "__main__":
    main()
