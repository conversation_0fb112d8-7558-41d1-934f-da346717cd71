{"timestamp": "2025-06-15T09:24:05.245882", "total_tested": 31, "working_count": 19, "models": [{"name": "gemini-1.0-pro-vision-latest", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "not_found", "error_message": "404 Gemini 1.0 Pro Vision has been deprecated on July 12, 2024. Consider switching to different model, for example gemini-1.5-flash.", "estimated_rpm": 60, "category": "legacy-vision", "recommendation": "УСТАРЕЛА"}, {"name": "gemini-pro-vision", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "not_found", "error_message": "404 Gemini 1.0 Pro Vision has been deprecated on July 12, 2024. Consider switching to different model, for example gemini-1.5-flash.", "estimated_rpm": 60, "category": "legacy-vision", "recommendation": "УСТАРЕЛА"}, {"name": "gemini-1.5-pro-latest", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 6\n}\n]", "estimated_rpm": 360, "category": "pro-1.5", "recommendation": "МЕДЛЕННО"}, {"name": "gemini-1.5-pro-002", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 6\n}\n]", "estimated_rpm": 360, "category": "pro-1.5", "recommendation": "МЕДЛЕННО"}, {"name": "gemini-1.5-pro", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-1.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 6\n}\n]", "estimated_rpm": 360, "category": "pro-1.5", "recommendation": "МЕДЛЕННО"}, {"name": "gemini-1.5-flash-latest", "available": true, "response_time": 0.4044334888458252, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "flash-1.5", "recommendation": "ХОРОШО"}, {"name": "gemini-1.5-flash", "available": true, "response_time": 0.32897353172302246, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "flash-1.5", "recommendation": "ХОРОШО"}, {"name": "gemini-1.5-flash-002", "available": true, "response_time": 0.37804198265075684, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "flash-1.5", "recommendation": "ХОРОШО"}, {"name": "gemini-1.5-flash-8b", "available": true, "response_time": 0.3360741138458252, "response_length": 16, "error_type": "", "error_message": "", "estimated_rpm": 2000, "category": "flash-8b", "recommendation": "🔥 ЛУЧШИЙ"}, {"name": "gemini-1.5-flash-8b-001", "available": true, "response_time": 0.3469374179840088, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 2000, "category": "flash-8b", "recommendation": "🔥 ЛУЧШИЙ"}, {"name": "gemini-1.5-flash-8b-latest", "available": true, "response_time": 0.34541964530944824, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 2000, "category": "flash-8b", "recommendation": "🔥 ЛУЧШИЙ"}, {"name": "gemini-2.5-pro-exp-03-25", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_requests_per_model_per_day\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel\"\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerDay-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 3\n}\n]", "estimated_rpm": 360, "category": "pro-2.5", "recommendation": "НОВЫЙ"}, {"name": "gemini-2.5-pro-preview-03-25", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 Gemini 2.5 Pro Preview doesn't have a free quota tier. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerDay-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 3\n}\n]", "estimated_rpm": 360, "category": "pro-2.5", "recommendation": "НОВЫЙ"}, {"name": "gemini-2.5-flash-preview-04-17", "available": true, "response_time": 0.6246118545532227, "response_length": 11, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "flash-2.5", "recommendation": "НОВЫЙ"}, {"name": "gemini-2.5-flash-preview-05-20", "available": true, "response_time": 0.35727882385253906, "response_length": 11, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "flash-2.5", "recommendation": "НОВЫЙ"}, {"name": "gemini-2.5-flash-preview-04-17-thinking", "available": true, "response_time": 0.5701589584350586, "response_length": 11, "error_type": "", "error_message": "", "estimated_rpm": 500, "category": "thinking", "recommendation": "МЕДЛЕННО"}, {"name": "gemini-2.5-pro-preview-05-06", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 Gemini 2.5 Pro Preview doesn't have a free quota tier. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerDay-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 1\n}\n]", "estimated_rpm": 360, "category": "pro-2.5", "recommendation": "НОВЫЙ"}, {"name": "gemini-2.5-pro-preview-06-05", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 Gemini 2.5 Pro Preview doesn't have a free quota tier. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerDay-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 1\n}\n]", "estimated_rpm": 360, "category": "pro-2.5", "recommendation": "НОВЫЙ"}, {"name": "gemini-2.0-flash-exp", "available": true, "response_time": 0.3681223392486572, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "flash-2.0", "recommendation": "ОТЛИЧНО"}, {"name": "gemini-2.0-flash", "available": true, "response_time": 0.44272470474243164, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "flash-2.0", "recommendation": "ОТЛИЧНО"}, {"name": "gemini-2.0-flash-001", "available": true, "response_time": 0.3885965347290039, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "flash-2.0", "recommendation": "ОТЛИЧНО"}, {"name": "gemini-2.0-flash-lite-001", "available": true, "response_time": 0.4089820384979248, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "lite-2.0", "recommendation": "БЫСТРО"}, {"name": "gemini-2.0-flash-lite", "available": true, "response_time": 0.38555288314819336, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "lite-2.0", "recommendation": "БЫСТРО"}, {"name": "gemini-2.0-flash-lite-preview-02-05", "available": true, "response_time": 0.37155723571777344, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "lite-2.0", "recommendation": "БЫСТРО"}, {"name": "gemini-2.0-flash-lite-preview", "available": true, "response_time": 0.360121488571167, "response_length": 17, "error_type": "", "error_message": "", "estimated_rpm": 1000, "category": "lite-2.0", "recommendation": "БЫСТРО"}, {"name": "gemini-2.0-pro-exp", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_requests_per_model_per_day\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel\"\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerDay-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 57\n}\n]", "estimated_rpm": 360, "category": "pro-2.0", "recommendation": "МЕДЛЕННО"}, {"name": "gemini-2.0-pro-exp-02-05", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerDay-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_requests_per_model_per_day\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel\"\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 57\n}\n]", "estimated_rpm": 360, "category": "pro-2.0", "recommendation": "МЕДЛЕННО"}, {"name": "gemini-exp-1206", "available": false, "response_time": 0.0, "response_length": 0, "error_type": "quota_exceeded", "error_message": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_requests_per_model_per_day\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel\"\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\nviolations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerDay-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.0-pro-exp\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 57\n}\n]", "estimated_rpm": 1000, "category": "experimental", "recommendation": "ОТЛИЧНО"}, {"name": "gemini-2.0-flash-thinking-exp-01-21", "available": true, "response_time": 0.7349541187286377, "response_length": 11, "error_type": "", "error_message": "", "estimated_rpm": 500, "category": "thinking", "recommendation": "МЕДЛЕННО"}, {"name": "gemini-2.0-flash-thinking-exp", "available": true, "response_time": 0.5979533195495605, "response_length": 11, "error_type": "", "error_message": "", "estimated_rpm": 500, "category": "thinking", "recommendation": "МЕДЛЕННО"}, {"name": "gemini-2.0-flash-thinking-exp-1219", "available": true, "response_time": 0.5922973155975342, "response_length": 11, "error_type": "", "error_message": "", "estimated_rpm": 500, "category": "thinking", "recommendation": "МЕДЛЕННО"}]}