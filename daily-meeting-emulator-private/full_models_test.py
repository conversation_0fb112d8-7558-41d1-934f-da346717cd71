#!/usr/bin/env python3
"""
ПОЛНАЯ ПРОВЕРКА ВСЕХ GEMINI МОДЕЛЕЙ
Тестирует все 39 доступных моделей и создает детальный отчет по квотам
"""

import google.generativeai as genai
import time
import json
import os
from dataclasses import dataclass, asdict

@dataclass
class ModelResult:
    name: str
    available: bool = False
    response_time: float = 0.0
    response_length: int = 0
    error: str = ""
    estimated_rpm: int = 0
    estimated_rpd: int = 0
    category: str = ""
    recommendation: str = ""

def test_all_models():
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY не установлен")
        return
    
    genai.configure(api_key=api_key)
    
    # ВСЕ 39 МОДЕЛЕЙ ИЗ СПИСКА
    all_models = [
        'gemini-1.0-pro-vision-latest',
        'gemini-pro-vision',
        'gemini-1.5-pro-latest',
        'gemini-1.5-pro-002',
        'gemini-1.5-pro',
        'gemini-1.5-flash-latest',
        'gemini-1.5-flash',
        'gemini-1.5-flash-002',
        'gemini-1.5-flash-8b',
        'gemini-1.5-flash-8b-001',
        'gemini-1.5-flash-8b-latest',
        'gemini-2.5-pro-exp-03-25',
        'gemini-2.5-pro-preview-03-25',
        'gemini-2.5-flash-preview-04-17',
        'gemini-2.5-flash-preview-05-20',
        'gemini-2.5-flash-preview-04-17-thinking',
        'gemini-2.5-pro-preview-05-06',
        'gemini-2.5-pro-preview-06-05',
        'gemini-2.0-flash-exp',
        'gemini-2.0-flash',
        'gemini-2.0-flash-001',
        'gemini-2.0-flash-lite-001',
        'gemini-2.0-flash-lite',
        'gemini-2.0-flash-lite-preview-02-05',
        'gemini-2.0-flash-lite-preview',
        'gemini-2.0-pro-exp',
        'gemini-2.0-pro-exp-02-05',
        'gemini-exp-1206',
        'gemini-2.0-flash-thinking-exp-01-21',
        'gemini-2.0-flash-thinking-exp',
        'gemini-2.0-flash-thinking-exp-1219',
        'gemini-2.5-flash-preview-tts',
        'gemini-2.5-pro-preview-tts',
        'learnlm-2.0-flash-experimental',
        'gemma-3-1b-it',
        'gemma-3-4b-it',
        'gemma-3-12b-it',
        'gemma-3-27b-it',
        'gemma-3n-e4b-it'
    ]
    
    # Известные квоты и характеристики
    model_specs = {
        # Gemini 1.0 (устаревшие)
        'gemini-1.0-pro-vision-latest': {'rpm': 60, 'rpd': 1500, 'cat': 'legacy', 'rec': 'УСТАРЕЛА'},
        'gemini-pro-vision': {'rpm': 60, 'rpd': 1500, 'cat': 'legacy', 'rec': 'УСТАРЕЛА'},
        
        # Gemini 1.5 Pro (средние квоты)
        'gemini-1.5-pro-latest': {'rpm': 360, 'rpd': 50000, 'cat': 'pro', 'rec': 'МЕДЛЕННО'},
        'gemini-1.5-pro-002': {'rpm': 360, 'rpd': 50000, 'cat': 'pro', 'rec': 'МЕДЛЕННО'},
        'gemini-1.5-pro': {'rpm': 360, 'rpd': 50000, 'cat': 'pro', 'rec': 'МЕДЛЕННО'},
        
        # Gemini 1.5 Flash (хорошие квоты)
        'gemini-1.5-flash-latest': {'rpm': 1000, 'rpd': 50000, 'cat': 'flash', 'rec': 'ХОРОШО'},
        'gemini-1.5-flash': {'rpm': 1000, 'rpd': 50000, 'cat': 'flash', 'rec': 'ХОРОШО'},
        'gemini-1.5-flash-002': {'rpm': 1000, 'rpd': 50000, 'cat': 'flash', 'rec': 'ХОРОШО'},
        
        # Gemini 1.5 Flash 8B (ЛУЧШИЕ квоты)
        'gemini-1.5-flash-8b': {'rpm': 2000, 'rpd': 50000, 'cat': 'flash-8b', 'rec': '🔥 ЛУЧШИЙ'},
        'gemini-1.5-flash-8b-001': {'rpm': 2000, 'rpd': 50000, 'cat': 'flash-8b', 'rec': '🔥 ЛУЧШИЙ'},
        'gemini-1.5-flash-8b-latest': {'rpm': 2000, 'rpd': 50000, 'cat': 'flash-8b', 'rec': '🔥 ЛУЧШИЙ'},
        
        # Gemini 2.0 Flash (отличные квоты)
        'gemini-2.0-flash-exp': {'rpm': 1000, 'rpd': 50000, 'cat': 'flash-2.0', 'rec': 'ОТЛИЧНО'},
        'gemini-2.0-flash': {'rpm': 1000, 'rpd': 50000, 'cat': 'flash-2.0', 'rec': 'ОТЛИЧНО'},
        'gemini-2.0-flash-001': {'rpm': 1000, 'rpd': 50000, 'cat': 'flash-2.0', 'rec': 'ОТЛИЧНО'},
        'gemini-exp-1206': {'rpm': 1000, 'rpd': 50000, 'cat': 'experimental', 'rec': 'ОТЛИЧНО'},
        
        # Gemini 2.0 Lite (быстрые)
        'gemini-2.0-flash-lite-001': {'rpm': 1000, 'rpd': 50000, 'cat': 'lite', 'rec': 'БЫСТРО'},
        'gemini-2.0-flash-lite': {'rpm': 1000, 'rpd': 50000, 'cat': 'lite', 'rec': 'БЫСТРО'},
        'gemini-2.0-flash-lite-preview-02-05': {'rpm': 1000, 'rpd': 50000, 'cat': 'lite', 'rec': 'БЫСТРО'},
        'gemini-2.0-flash-lite-preview': {'rpm': 1000, 'rpd': 50000, 'cat': 'lite', 'rec': 'БЫСТРО'},
        
        # Gemini 2.0 Pro (средние квоты)
        'gemini-2.0-pro-exp': {'rpm': 360, 'rpd': 50000, 'cat': 'pro-2.0', 'rec': 'МЕДЛЕННО'},
        'gemini-2.0-pro-exp-02-05': {'rpm': 360, 'rpd': 50000, 'cat': 'pro-2.0', 'rec': 'МЕДЛЕННО'},
        
        # Gemini 2.5 (новейшие)
        'gemini-2.5-pro-exp-03-25': {'rpm': 360, 'rpd': 50000, 'cat': 'pro-2.5', 'rec': 'НОВЫЙ'},
        'gemini-2.5-pro-preview-03-25': {'rpm': 360, 'rpd': 50000, 'cat': 'pro-2.5', 'rec': 'НОВЫЙ'},
        'gemini-2.5-flash-preview-04-17': {'rpm': 1000, 'rpd': 50000, 'cat': 'flash-2.5', 'rec': 'НОВЫЙ'},
        'gemini-2.5-flash-preview-05-20': {'rpm': 1000, 'rpd': 50000, 'cat': 'flash-2.5', 'rec': 'НОВЫЙ'},
        'gemini-2.5-pro-preview-05-06': {'rpm': 360, 'rpd': 50000, 'cat': 'pro-2.5', 'rec': 'НОВЫЙ'},
        'gemini-2.5-pro-preview-06-05': {'rpm': 360, 'rpd': 50000, 'cat': 'pro-2.5', 'rec': 'НОВЫЙ'},
        
        # Thinking модели
        'gemini-2.0-flash-thinking-exp-01-21': {'rpm': 500, 'rpd': 25000, 'cat': 'thinking', 'rec': 'МЕДЛЕННО'},
        'gemini-2.0-flash-thinking-exp': {'rpm': 500, 'rpd': 25000, 'cat': 'thinking', 'rec': 'МЕДЛЕННО'},
        'gemini-2.0-flash-thinking-exp-1219': {'rpm': 500, 'rpd': 25000, 'cat': 'thinking', 'rec': 'МЕДЛЕННО'},
        'gemini-2.5-flash-preview-04-17-thinking': {'rpm': 500, 'rpd': 25000, 'cat': 'thinking', 'rec': 'МЕДЛЕННО'},
        
        # TTS модели
        'gemini-2.5-flash-preview-tts': {'rpm': 1000, 'rpd': 50000, 'cat': 'tts', 'rec': 'СПЕЦИАЛЬНЫЙ'},
        'gemini-2.5-pro-preview-tts': {'rpm': 360, 'rpd': 50000, 'cat': 'tts', 'rec': 'СПЕЦИАЛЬНЫЙ'},
        
        # LearnLM
        'learnlm-2.0-flash-experimental': {'rpm': 1000, 'rpd': 50000, 'cat': 'learn', 'rec': 'СПЕЦИАЛЬНЫЙ'},
        
        # Gemma модели (открытые)
        'gemma-3-1b-it': {'rpm': 1000, 'rpd': 50000, 'cat': 'gemma', 'rec': 'ОТКРЫТАЯ'},
        'gemma-3-4b-it': {'rpm': 1000, 'rpd': 50000, 'cat': 'gemma', 'rec': 'ОТКРЫТАЯ'},
        'gemma-3-12b-it': {'rpm': 500, 'rpd': 25000, 'cat': 'gemma', 'rec': 'ОТКРЫТАЯ'},
        'gemma-3-27b-it': {'rpm': 200, 'rpd': 10000, 'cat': 'gemma', 'rec': 'ОТКРЫТАЯ'},
        'gemma-3n-e4b-it': {'rpm': 1000, 'rpd': 50000, 'cat': 'gemma', 'rec': 'ОТКРЫТАЯ'},
    }
    
    print('🔍 ПОЛНАЯ ПРОВЕРКА ВСЕХ 39 GEMINI МОДЕЛЕЙ')
    print('=' * 80)
    print(f'📋 Всего моделей для тестирования: {len(all_models)}')
    print()
    
    results = []
    working_models = []
    failed_models = []
    
    for i, model_name in enumerate(all_models, 1):
        print(f'[{i:2d}/{len(all_models)}] Тестируем {model_name}...')
        
        result = ModelResult(name=model_name)
        specs = model_specs.get(model_name, {'rpm': 60, 'rpd': 1500, 'cat': 'unknown', 'rec': 'НЕИЗВЕСТНО'})
        
        result.estimated_rpm = specs['rpm']
        result.estimated_rpd = specs['rpd']
        result.category = specs['cat']
        result.recommendation = specs['rec']
        
        try:
            start_time = time.time()
            model = genai.GenerativeModel(model_name)
            
            # Тест с коротким промптом
            response = model.generate_content(
                'Say hello briefly.',
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=30,
                    temperature=0.5
                )
            )
            
            response_time = time.time() - start_time
            
            if response.text and len(response.text.strip()) > 0:
                result.available = True
                result.response_time = response_time
                result.response_length = len(response.text)
                working_models.append(result)
                
                print(f'   ✅ РАБОТАЕТ: {response_time:.2f}s, {len(response.text)} символов')
                print(f'   📊 Квота: {specs["rpm"]} req/min | {specs["rec"]}')
            else:
                result.error = "Пустой ответ"
                failed_models.append(result)
                print(f'   ❌ Пустой ответ')
                
        except Exception as e:
            error_msg = str(e)
            result.error = error_msg
            failed_models.append(result)
            
            if '429' in error_msg:
                print(f'   ⚠️  КВОТА ПРЕВЫШЕНА')
            elif '404' in error_msg:
                print(f'   🗑️  МОДЕЛЬ УСТАРЕЛА/НЕДОСТУПНА')
            else:
                print(f'   ❌ ОШИБКА: {error_msg[:50]}...')
        
        results.append(result)
        time.sleep(0.2)  # Небольшая задержка между запросами
    
    # Генерируем полный отчет
    generate_full_report(working_models, failed_models, results)

def generate_full_report(working_models, failed_models, all_results):
    print('\n' + '='*80)
    print('📊 ПОЛНЫЙ ОТЧЕТ ПО ВСЕМ GEMINI МОДЕЛЯМ')
    print('='*80)
    
    print(f'✅ Работающих моделей: {len(working_models)}')
    print(f'❌ Недоступных моделей: {len(failed_models)}')
    print(f'📋 Всего протестировано: {len(all_results)}')
    print()
    
    # Группировка по категориям
    categories = {}
    for model in working_models:
        cat = model.category
        if cat not in categories:
            categories[cat] = []
        categories[cat].append(model)
    
    print('📂 РАБОТАЮЩИЕ МОДЕЛИ ПО КАТЕГОРИЯМ:')
    print('-' * 60)
    
    for category, models in categories.items():
        avg_rpm = sum(m.estimated_rpm for m in models) / len(models)
        print(f'\n🔸 {category.upper()} ({len(models)} моделей)')
        print(f'   Средняя квота: {avg_rpm:.0f} запросов/минуту')
        
        # Сортируем по квоте
        models.sort(key=lambda x: x.estimated_rpm, reverse=True)
        for model in models:
            print(f'   • {model.name}: {model.estimated_rpm} req/min ({model.recommendation})')
    
    # Топ рекомендации
    print(f'\n🏆 ТОП-10 МОДЕЛЕЙ ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ:')
    print('-' * 60)
    
    # Сортируем по квоте и скорости
    working_models.sort(key=lambda x: (x.estimated_rpm, -x.response_time), reverse=True)
    
    for i, model in enumerate(working_models[:10], 1):
        print(f'{i:2d}. {model.name}')
        print(f'    📊 {model.estimated_rpm:,} req/min = {model.estimated_rpm/60:.1f} req/sec')
        print(f'    ⚡ {model.response_time:.2f}s | 🎯 {model.recommendation}')
        print()
    
    # Анализ по квотам
    print('💰 АНАЛИЗ ПО УРОВНЯМ КВОТ:')
    print('-' * 40)
    
    ultra_high = [m for m in working_models if m.estimated_rpm >= 2000]
    high = [m for m in working_models if 1000 <= m.estimated_rpm < 2000]
    medium = [m for m in working_models if 100 <= m.estimated_rpm < 1000]
    low = [m for m in working_models if m.estimated_rpm < 100]
    
    print(f'🔥 Ультра-высокие (≥2000): {len(ultra_high)} - ИДЕАЛЬНО ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ')
    print(f'🟢 Высокие (1000-1999): {len(high)} - ОТЛИЧНО ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ')
    print(f'🟡 Средние (100-999): {len(medium)} - ПОДХОДЯТ С ОГРАНИЧЕНИЯМИ')
    print(f'🔴 Низкие (<100): {len(low)} - НЕ ПОДХОДЯТ ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ')
    
    # Финальная рекомендация
    if working_models:
        best = working_models[0]
        print(f'\n🎯 ИТОГОВАЯ РЕКОМЕНДАЦИЯ:')
        print('='*50)
        print(f'🏆 ЛУЧШАЯ МОДЕЛЬ: {best.name}')
        print(f'📊 Квота: {best.estimated_rpm:,} запросов/минуту')
        print(f'⚡ Скорость: {best.response_time:.2f} секунды')
        print(f'🎯 Оценка: {best.recommendation}')
        print(f'💡 Для диалога: до {best.estimated_rpm/60*0.8:.1f} запросов/секунду')
        print(f'🛡️  Безопасный лимит: {best.estimated_rpm*0.9:.0f} запросов/минуту')
        
        print(f'\n🔄 ИНФОРМАЦИЯ О ВОССТАНОВЛЕНИИ КВОТ:')
        print('• Минутные лимиты: Скользящее окно 60 секунд')
        print('• Дневные лимиты: Сброс в полночь UTC')
        print('• Burst запросы: Кратковременные всплески разрешены')
    
    # Сохраняем результаты
    save_results(all_results)

def save_results(results):
    data = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'total_tested': len(results),
        'working_count': len([r for r in results if r.available]),
        'models': [asdict(r) for r in results]
    }
    
    with open('gemini_models_full_test.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f'\n💾 Результаты сохранены в gemini_models_full_test.json')

if __name__ == "__main__":
    test_all_models()
